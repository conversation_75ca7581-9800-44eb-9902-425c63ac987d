<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-blue-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-blue-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Pending</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-clock text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Pending Bookings</h1>
                    <p class="text-blue-100">Bookings awaiting approval from operators</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Booking Cards Container -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="bookingContainer">
            <?php
            try {
                $getResortStatus = "Pending";

                $bookingDetails = getBookingDetails($pdo, $getResortStatus, $id);

                if ($bookingDetails) {
                    foreach ($bookingDetails as $row) {
                        $booking_id = $row['booking_id'];
                        $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                        $extname = $row['extname'] ?? ''; // Use null coalescing operator for defaults
                        $middlename = $row['middlename'] ?? '';
                        $designationTour = $row['tour_operator_designation'];
                        $resortName = $row['resort_operator_designation'];
                        $referenceNumber = $row['referenceNum'];
                        $boatName = $row['boatName'];
                        $portName = $row['portName'];
                        $checkIn = $row['check_in_date'];
                        $checkout = $row['check_out_date'];
                        $adultCount = $row['total_adults'];
                        $childrenCount = $row['total_children'];
                        $cancellation = $row['cancellation_booking_status'];

                        $isCanceled = isset($row['cancellation_booking_status']) && $row['cancellation_booking_status'] === 'request';

                        $showMessage = $isCanceled ? "Pending Cancellation Process" : "";
                        ?>
                        <!-- Booking Card -->
                        <div class="booking-card bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-all duration-200"
                            data-reference="<?= htmlspecialchars($referenceNumber ?? ''); ?>"
                            data-operator="<?= htmlspecialchars($operatorName ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['resort_operator_designation'] ?? ''); ?>" data-status="pending">
                            <!-- Reference Number -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                    </h3>
                                    <div class="status-badge status-pending">
                                        <i class="fas fa-clock mr-1"></i>
                                        Awaiting Approval
                                    </div>
                                </div>
                            </div>

                            <!-- Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-building text-gray-400 w-5 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['resort_operator_designation']); ?></span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-ship text-gray-400 w-5 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($boatName ?? 'N/A'); ?></span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-2">
                                <button data-modal-target="view-booking-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="view-booking-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </button>
                            </div>
                        </div>
                        <!-- View Booking Modal -->
                        <div id="view-booking-modal-<?= $row['booking_id']; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                            <div class="relative w-full max-w-4xl max-h-full">
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-600 to-blue-800 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-hotel text-white mr-2"></i>
                                            <h3 class="text-lg font-semibold text-white">
                                                Resort Booking Details
                                            </h3>
                                        </div>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-blue-800 hover:bg-blue-900"
                                            data-modal-hide="view-booking-modal-<?= $row['booking_id']; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>

                                    <!-- Reference Number Banner -->
                                    <div class="bg-blue-50 p-3 border-b border-blue-100">
                                        <div class="flex justify-between items-center">
                                            <div class="flex items-center">
                                                <i class="fas fa-hashtag text-blue-600 mr-2"></i>
                                                <span class="text-sm font-medium text-blue-800">Reference Number:</span>
                                            </div>
                                            <span class="text-sm font-bold text-blue-600"><?= $referenceNumber; ?></span>
                                        </div>
                                    </div>

                                    <!-- Cancellation Status Alert -->
                                    <?php if (!empty($showMessage)): ?>
                                        <div class="bg-red-50 border-l-4 border-red-400 p-4">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-exclamation-triangle text-red-400"></i>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm font-medium text-red-800">
                                                        <?= htmlspecialchars($showMessage); ?>
                                                    </p>
                                                    <p class="text-xs text-red-600 mt-1">
                                                        This booking has a pending cancellation request.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Modal Body -->
                                    <form action="inc/inc.booking.php" method="POST">
                                        <div class="p-3 bg-white">
                                            <!-- Response Section - Moved to Top for Mobile -->
                                            <div class="bg-blue-50 p-3 rounded-lg border border-blue-200 mb-3">
                                                <div class="flex items-center mb-2">
                                                    <i class="fas fa-reply text-blue-600 mr-2"></i>
                                                    <h4 class="text-sm font-semibold text-blue-800">Your Response</h4>
                                                </div>
                                                <div class="relative">
                                                    <div
                                                        class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                        <i class="fas fa-check-circle text-gray-400"></i>
                                                    </div>
                                                    <select id="resort-<?= $row['booking_id']; ?>" name="resortResponse"
                                                        class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                                                        required onchange="handleResortResponse(this, <?= $row['booking_id']; ?>)">
                                                        <option selected disabled value="">Select your response...</option>
                                                        <option value="Approved">Approve</option>
                                                        <option value="Declined">Decline</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-3">
                                                <!-- Operator & Destination Information -->
                                                <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                    <div class="flex items-center mb-2">
                                                        <i class="fas fa-user-tie text-blue-600 mr-2"></i>
                                                        <h4 class="text-sm font-semibold text-gray-800">Operator Information</h4>
                                                    </div>
                                                    <div class="space-y-1">
                                                        <div class="flex justify-between items-center py-1">
                                                            <span class="text-xs text-gray-600"><i
                                                                    class="fas fa-user text-gray-400 mr-1"></i>Tour Operator:</span>
                                                            <span
                                                                class="text-xs font-medium text-gray-800"><?= $operatorName; ?></span>
                                                        </div>
                                                        <div class="flex justify-between items-center py-1">
                                                            <span class="text-xs text-gray-600"><i
                                                                    class="fas fa-building text-gray-400 mr-1"></i>Designation:</span>
                                                            <span
                                                                class="text-xs font-medium text-gray-800"><?= $designationTour; ?></span>
                                                        </div>
                                                        <div class="flex justify-between items-center py-1">
                                                            <span class="text-xs text-gray-600"><i
                                                                    class="fas fa-hotel text-gray-400 mr-1"></i>Resort:</span>
                                                            <span
                                                                class="text-xs font-medium text-gray-800"><?= $resortName; ?></span>
                                                        </div>
                                                        <div class="flex justify-between items-center py-1">
                                                            <span class="text-xs text-gray-600"><i
                                                                    class="fas fa-map-marker-alt text-gray-400 mr-1"></i>Departure:</span>
                                                            <span class="text-xs font-medium text-gray-800"><?= $portName; ?></span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Transportation & Schedule -->
                                                <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                    <div class="flex items-center mb-2">
                                                        <i class="fas fa-ship text-blue-600 mr-2"></i>
                                                        <h4 class="text-sm font-semibold text-gray-800">Transportation & Schedule
                                                        </h4>
                                                    </div>
                                                    <div class="space-y-1">
                                                        <div class="flex justify-between items-center py-1">
                                                            <span class="text-xs text-gray-600"><i
                                                                    class="fas fa-ship text-gray-400 mr-1"></i>Boat:</span>
                                                            <span class="text-xs font-medium text-gray-800"><?= $boatName; ?></span>
                                                        </div>
                                                        <div class="flex justify-between items-center py-1">
                                                            <span class="text-xs text-gray-600"><i
                                                                    class="fas fa-calendar-check text-gray-400 mr-1"></i>Check-in:</span>
                                                            <span
                                                                class="text-xs font-medium text-gray-800"><?= (new DateTime($checkIn))->format('M d, Y'); ?></span>
                                                        </div>
                                                        <div class="flex justify-between items-center py-1">
                                                            <span class="text-xs text-gray-600"><i
                                                                    class="fas fa-calendar-minus text-gray-400 mr-1"></i>Check-out:</span>
                                                            <span
                                                                class="text-xs font-medium text-gray-800"><?= (new DateTime($checkout))->format('M d, Y'); ?></span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Passenger Information -->
                                                <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                    <div class="flex items-center mb-2">
                                                        <i class="fas fa-users text-blue-600 mr-2"></i>
                                                        <h4 class="text-sm font-semibold text-gray-800">Passenger Information</h4>
                                                    </div>
                                                    <div class="space-y-1">
                                                        <div class="flex justify-between items-center py-1">
                                                            <span class="text-xs text-gray-600"><i
                                                                    class="fas fa-user text-gray-400 mr-1"></i>Adults:</span>
                                                            <span
                                                                class="text-xs font-medium text-gray-800"><?= $adultCount; ?></span>
                                                        </div>
                                                        <div class="flex justify-between items-center py-1">
                                                            <span class="text-xs text-gray-600"><i
                                                                    class="fas fa-child text-gray-400 mr-1"></i>Children:</span>
                                                            <span
                                                                class="text-xs font-medium text-gray-800"><?= $childrenCount; ?></span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <?php
                                                // Check approval status
                                                $approvalQuery = "SELECT resort, boat FROM cb_booking_approvals WHERE booking_id = :booking_id";
                                                $approvalStmt = $pdo->prepare($approvalQuery);
                                                $approvalStmt->execute([':booking_id' => $booking_id]);
                                                $approvalStatus = $approvalStmt->fetch(PDO::FETCH_ASSOC);

                                                $resortApproved = ($approvalStatus && $approvalStatus['resort'] === 'Approved');
                                                $boatApproved = ($approvalStatus && $approvalStatus['boat'] === 'Approved');
                                                $bothApproved = ($resortApproved && $boatApproved);

                                                // Get passenger counts
                                                $countsQuery = "SELECT
                                                            SUM(CASE WHEN info_type = 'tourist' THEN 1 ELSE 0 END) as adults,
                                                            SUM(CASE WHEN info_type = 'crewTts' THEN 1 ELSE 0 END) as crew
                                                            FROM cb_tourists
                                                            WHERE booking_id = :booking_id";
                                                $countsStmt = $pdo->prepare($countsQuery);
                                                $countsStmt->execute([':booking_id' => $booking_id]);
                                                $counts = $countsStmt->fetch(PDO::FETCH_ASSOC);

                                                $hasEnoughPassengers = ($counts && $counts['adults'] >= 1 && $counts['crew'] >= 1);
                                                ?>


                                            </div>

                                            <!-- Form Section -->
                                            <input type="hidden" name="csrf_token"
                                                value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                            <input type="hidden" name="booking_id" value="<?= $booking_id; ?>">
                                            <input type="hidden" name="referenceNumber" value="<?= $referenceNumber; ?>">
                                        </div>

                                        <!-- Modal Footer -->
                                        <div class="p-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                            <div class="grid grid-cols-2 gap-2">
                                                <button data-modal-hide="view-booking-modal-<?= $row['booking_id']; ?>"
                                                    type="button" class="action-button btn-close pending">
                                                    <i class="fas fa-times-circle mr-1.5"></i> Close
                                                </button>
                                                <button type="submit" name="resortResponseBtn"
                                                    id="submitResortBtn-<?= $row['booking_id']; ?>"
                                                    class="action-button btn-submit pending">
                                                    <i class="fas fa-paper-plane mr-1.5"></i> Submit Response
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Decline Reason Modal -->
                        <div id="decline-reason-modal-<?= $row['booking_id']; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full backdrop-blur-sm bg-gray-900/50">
                            <div class="relative w-full max-w-lg max-h-full">
                                <div class="relative bg-white rounded-lg shadow-lg">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-4 bg-gradient-to-r from-red-600 to-red-700 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-exclamation-triangle text-white mr-2"></i>
                                            <h3 class="text-xl font-semibold text-white">Decline Booking</h3>
                                        </div>
                                        <button type="button" data-modal-hide="decline-reason-modal-<?= $row['booking_id']; ?>"
                                            class="modal-close-reload text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-red-800 hover:bg-red-900">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>
                                    <!-- Modal Body -->
                                    <form action="inc/inc.booking.php" method="POST" id="declineForm-<?= $row['booking_id']; ?>">
                                        <div class="p-5 bg-white rounded-lg space-y-4">
                                            <div class="bg-gray-800 text-white p-4 rounded-lg">
                                                <h4 class="text-lg font-semibold mb-2">Boat/Resort Operator:</h4>
                                                <span>Must give a reason for declining a booking.</span>
                                                <div class="flex items-center my-4 mb-2">
                                                    <span class="text-sm">Choose a reason or write your own:</span>
                                                </div>
                                                <div class="space-y-2 mb-4">
                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="decline_reason" value="Fully booked"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Fully booked</span>
                                                    </label>
                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="decline_reason" value="Maintenance"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Maintenance</span>
                                                    </label>
                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="decline_reason" value="Staff unavailable"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Staff unavailable</span>
                                                    </label>
                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="decline_reason" value="Double booking"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Double booking</span>
                                                    </label>
                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="decline_reason" value="other"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Other: [Text box]</span>
                                                    </label>
                                                </div>
                                                <div class="mt-3">
                                                    <textarea name="custom_decline_reason"
                                                        id="customDeclineReason-<?= $row['booking_id']; ?>"
                                                        placeholder="Please specify your reason..."
                                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-400 focus:border-pink-400 text-gray-900 text-sm bg-gray-100 text-gray-500"
                                                        rows="3" disabled></textarea>
                                                </div>
                                            </div>
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                                                <p class="text-xs text-gray-500 uppercase font-medium">Reference Number</p>
                                                <p class="text-sm font-semibold text-gray-800 mt-1">
                                                    <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                                </p>
                                            </div>
                                            <div class="space-y-2">
                                                <div class="flex justify-between">
                                                    <span class="text-sm text-gray-600">Resort:</span>
                                                    <span
                                                        class="text-sm font-medium text-gray-900"><?= htmlspecialchars($row['resort_operator_designation']); ?></span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span class="text-sm text-gray-600">Boat:</span>
                                                    <span
                                                        class="text-sm font-medium text-gray-900"><?= htmlspecialchars($boatName); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token']; ?>">
                                        <input type="hidden" name="booking_id" value="<?= $row['booking_id']; ?>">
                                        <input type="hidden" name="referenceNumber"
                                            value="<?= htmlspecialchars($row['referenceNum']); ?>">
                                        <input type="hidden" name="resortResponse" value="Declined">
                                        <div
                                            class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg space-x-3">
                                            <button data-modal-hide="decline-reason-modal-<?= $row['booking_id']; ?>" type="button"
                                                class="modal-close-reload py-2 px-4 text-sm font-semibold text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                <i class="fas fa-times mr-1"></i> Cancel
                                            </button>
                                            <button type="submit" name="resortResponseBtn"
                                                id="submitDeclineBtn-<?= $row['booking_id']; ?>"
                                                class="py-2 px-4 text-sm font-semibold text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200 opacity-50 cursor-not-allowed"
                                                disabled>
                                                <i class="fas fa-paper-plane mr-1"></i> Submit Decline
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <?php
                    }
                }
            } catch (PDOException $e) {
                ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                    <div class="bg-red-100 p-3 rounded-full mb-3 inline-block">
                        <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                    </div>
                    <p class="text-red-600 font-medium">
                        Error: <?= htmlspecialchars($e->getMessage()); ?>
                    </p>
                </div>
                <?php
            }

            if (empty($bookingDetails)) {
                ?>

                <div class="col-span-full">
                    <div class="booking-card rounded-lg p-8 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <div class="bg-blue-100 p-4 rounded-full mb-4">
                                <i class="fas fa-clock text-blue-500 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Pending Bookings</h3>
                            <p class="text-gray-500 text-sm mb-4 max-w-sm">Pending bookings will appear here when they are
                                awaiting approval.</p>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>
    <?php
    require '_footer.php';
    ?>