<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-gray-600 to-gray-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-gray-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3 text-gray-300 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Monitoring</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-chart-line text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Booking Monitoring</h1>
                    <p class="text-gray-100">Monitor and manage bookings awaiting MTHO approval</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Booking Cards Container -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="bookingContainer">
            <?php
            try {
                $getMthoStatus = "Waiting";
                $getBookingStatus = "pending";
                $bookingDetails = getBookingDetails($pdo, $getMthoStatus, $getBookingStatus);

                if ($bookingDetails && count($bookingDetails) > 0) {
                    foreach ($bookingDetails as $row) {
                        $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                        $extname = $row['extname'] ?? '';
                        $middlename = $row['middlename'] ?? '';
                        $designationTour = $row['tour_operator_designation'];
                        $resortName = $row['resort_operator_designation'];
                        $referenceNumber = $row['referenceNum'];
                        $boatName = $row['boatName'];
                        $portName = $row['portName'];
                        $checkIn = $row['check_in_date'];
                        $checkout = $row['check_out_date'];
                        $adultCount = $row['total_adults'];
                        $childrenCount = $row['total_children'];
                        $crewCount = $row['total_crew'];
            ?>
                        <!-- Booking Card -->
                        <div class="booking-card bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-all duration-200"
                            data-reference="<?= htmlspecialchars($referenceNumber ?? ''); ?>"
                            data-operator="<?= htmlspecialchars($operatorName ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['resort_operator_designation'] ?? ''); ?>" data-status="monitoring">
                            <!-- Reference Number -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                    </h3>
                                    <div class="status-badge bg-yellow-100 text-yellow-800 border border-yellow-300">
                                        <i class="fas fa-clock mr-1"></i>
                                        Awaiting MTHO Approval
                                    </div>
                                </div>
                            </div>

                            <!-- Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-user-tie text-gray-400 w-5 mr-3"></i>
                                    <span class="text-gray-600">Operator:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($operatorName); ?></span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-building text-gray-400 w-5 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['resort_operator_designation']); ?></span>
                                </div>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fas fa-ship text-gray-400 w-5 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($boatName ?? 'N/A'); ?></span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex">
                                <button data-modal-target="view-approvals-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="view-approvals-modal-<?= $row['booking_id']; ?>" type="button"
                                    class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fas fa-chart-line mr-2"></i>
                                    Monitor Details
                                </button>
                            </div>
                        </div>
                        <!-- View Booking Modal -->
                        <div id="view-approvals-modal-<?= $row['booking_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                            <div class="relative w-full max-w-4xl max-h-full">
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                    <!-- Modal Header -->
                                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-600 to-gray-800 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-chart-line text-white mr-2"></i>
                                            <h3 class="text-lg font-semibold text-white">
                                                Booking Monitoring Details
                                            </h3>
                                        </div>
                                        <button type="button" class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-gray-800 hover:bg-gray-900" data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>

                                    <!-- Reference Number Banner -->
                                    <div class="bg-blue-50 p-3 border-b border-blue-100">
                                        <div class="flex justify-between items-center">
                                            <div class="flex items-center">
                                                <i class="fas fa-hashtag text-blue-600 mr-2"></i>
                                                <span class="text-sm font-medium text-blue-800">Reference Number:</span>
                                            </div>
                                            <span class="text-sm font-bold text-blue-600"><?= $referenceNumber; ?></span>
                                        </div>
                                    </div>

                                    <!-- Modal Body -->
                                    <div class="p-3 bg-white">
                                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-3">
                                            <!-- Operator & Destination Information -->
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                <div class="flex items-center mb-2">
                                                    <i class="fas fa-user-tie text-blue-600 mr-2"></i>
                                                    <h4 class="text-sm font-semibold text-gray-800">Operator Information</h4>
                                                </div>
                                                <div class="space-y-1">
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-user text-gray-400 mr-1"></i>Tour Operator:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= $operatorName; ?></span>
                                                    </div>
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-building text-gray-400 mr-1"></i>Designation:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= $designationTour; ?></span>
                                                    </div>
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-hotel text-gray-400 mr-1"></i>Resort:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= $resortName; ?></span>
                                                    </div>
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-map-marker-alt text-gray-400 mr-1"></i>Departure:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= $portName; ?></span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Transportation & Schedule -->
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                <div class="flex items-center mb-2">
                                                    <i class="fas fa-ship text-blue-600 mr-2"></i>
                                                    <h4 class="text-sm font-semibold text-gray-800">Transportation & Schedule</h4>
                                                </div>
                                                <div class="space-y-1">
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-ship text-gray-400 mr-1"></i>Boat:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= $boatName; ?></span>
                                                    </div>
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-calendar-check text-gray-400 mr-1"></i>Check-in:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= (new DateTime($checkIn))->format('M d, Y'); ?></span>
                                                    </div>
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-calendar-minus text-gray-400 mr-1"></i>Check-out:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= (new DateTime($checkout))->format('M d, Y'); ?></span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Passenger Information -->
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                <div class="flex items-center mb-2">
                                                    <i class="fas fa-users text-blue-600 mr-2"></i>
                                                    <h4 class="text-sm font-semibold text-gray-800">Passenger Information</h4>
                                                </div>
                                                <div class="space-y-1">
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-user text-gray-400 mr-1"></i>Adults:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= $adultCount; ?></span>
                                                    </div>
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-child text-gray-400 mr-1"></i>Children:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= $childrenCount; ?></span>
                                                    </div>
                                                    <div class="flex justify-between items-center py-1">
                                                        <span class="text-xs text-gray-600"><i class="fas fa-users text-gray-400 mr-1"></i>Crew:</span>
                                                        <span class="text-xs font-medium text-gray-800"><?= $crewCount; ?></span>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                            <!-- Approval Status Section -->
                                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 md:col-span-2">
                                                <div class="flex items-center mb-3">
                                                    <i class="fas fa-clipboard-check text-blue-600 mr-2"></i>
                                                    <h4 class="text-sm font-semibold text-gray-800">Approval Status</h4>
                                                </div>
                                                <div class="grid grid-cols-3 gap-3">
                                                    <?php
                                                    // Define status colors and icons
                                                    $statusColors = [
                                                        'Approved' => 'green',
                                                        'Waiting' => 'yellow',
                                                        'Declined' => 'red'
                                                    ];

                                                    $statusIcons = [
                                                        'Approved' => 'check-circle',
                                                        'Waiting' => 'clock',
                                                        'Declined' => 'times-circle'
                                                    ];

                                                    // Resort Status
                                                    $resortStatus = $row['resort'];
                                                    $resortColor = isset($statusColors[$resortStatus]) ? $statusColors[$resortStatus] : 'gray';
                                                    $resortIcon = isset($statusIcons[$resortStatus]) ? $statusIcons[$resortStatus] : 'question-circle';

                                                    // Boat Status
                                                    $boatStatus = $row['boat'];
                                                    $boatColor = isset($statusColors[$boatStatus]) ? $statusColors[$boatStatus] : 'gray';
                                                    $boatIcon = isset($statusIcons[$boatStatus]) ? $statusIcons[$boatStatus] : 'question-circle';

                                                    // Treasurer Status
                                                    $treasurerStatus = $row['treasurer'];
                                                    $treasurerColor = isset($statusColors[$treasurerStatus]) ? $statusColors[$treasurerStatus] : 'gray';
                                                    $treasurerIcon = isset($statusIcons[$treasurerStatus]) ? $statusIcons[$treasurerStatus] : 'question-circle';
                                                    ?>

                                                    <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                        <div class="flex items-center justify-between">
                                                            <p class="text-xs font-medium text-gray-700">Resort Status:</p>
                                                            <div class="w-6 h-6 rounded-full bg-<?= $resortColor; ?>-100 flex items-center justify-center">
                                                                <i class="fas fa-<?= $resortIcon; ?> text-<?= $resortColor; ?>-600 text-xs"></i>
                                                            </div>
                                                        </div>
                                                        <p class="text-xs font-semibold text-<?= $resortColor; ?>-600 mt-2 text-center"><?= $resortStatus; ?></p>
                                                    </div>

                                                    <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                        <div class="flex items-center justify-between">
                                                            <p class="text-xs font-medium text-gray-700">Boat Status:</p>
                                                            <div class="w-6 h-6 rounded-full bg-<?= $boatColor; ?>-100 flex items-center justify-center">
                                                                <i class="fas fa-<?= $boatIcon; ?> text-<?= $boatColor; ?>-600 text-xs"></i>
                                                            </div>
                                                        </div>
                                                        <p class="text-xs font-semibold text-<?= $boatColor; ?>-600 mt-2 text-center"><?= $boatStatus; ?></p>
                                                    </div>

                                                    <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                        <div class="flex items-center justify-between">
                                                            <p class="text-xs font-medium text-gray-700">Treasurer Status:</p>
                                                            <div class="w-6 h-6 rounded-full bg-<?= $treasurerColor; ?>-100 flex items-center justify-center">
                                                                <i class="fas fa-<?= $treasurerIcon; ?> text-<?= $treasurerColor; ?>-600 text-xs"></i>
                                                            </div>
                                                        </div>
                                                        <p class="text-xs font-semibold text-<?= $treasurerColor; ?>-600 mt-2 text-center"><?= $treasurerStatus; ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                    </div>

                                    <!-- Modal Footer -->
                                    <div class="p-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                        <div class="flex justify-end">
                                            <button data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>" type="button" class="action-button btn-close pending">
                                                <i class="fas fa-times-circle mr-1.5"></i> Close
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
            <?php
                    }
                } else {
                    // No bookings to monitor
            ?>
                    <div class="col-span-full">
                        <div class="booking-card rounded-lg p-8 text-center">
                            <div class="flex flex-col items-center justify-center">
                                <div class="bg-gray-100 p-4 rounded-full mb-4">
                                    <i class="fas fa-chart-line text-gray-500 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Bookings to Monitor</h3>
                                <p class="text-gray-500 text-sm mb-4 max-w-sm">Bookings awaiting MTHO approval will appear here when available.</p>
                            </div>
                        </div>
                    </div>
            <?php
                }
            } catch (PDOException $e) {
            ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                    <div class="bg-red-100 p-3 rounded-full mb-3 inline-block">
                        <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                    </div>
                    <p class="text-red-600 font-medium">
                        Error: <?= htmlspecialchars($e->getMessage()); ?>
                    </p>
                </div>
            <?php
            }
            ?>
        </div>
    </div>


<?php
require '_footer.php';
?>
