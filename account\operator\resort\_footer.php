<!-- Footer -->
<footer class="mt-auto p-4 bg-white md:p-6 md:flex md:items-center md:justify-between border-t border-gray-200">
    <span class="text-sm text-gray-500 sm:text-center">© <?= date('Y') ?> <a href="#" class="hover:underline">Calaguas
            TDF</a>. All Rights Reserved.
    </span>
    <div class="flex mt-4 space-x-6 sm:justify-center md:mt-0">
        <a href="#" class="text-gray-400 hover:text-blue-600">
            <i class="fab fa-facebook-f"></i>
            <span class="sr-only">Facebook page</span>
        </a>
        <a href="#" class="text-gray-400 hover:text-blue-600">
            <i class="fab fa-instagram"></i>
            <span class="sr-only">Instagram page</span>
        </a>
        <a href="#" class="text-gray-400 hover:text-blue-600">
            <i class="fab fa-twitter"></i>
            <span class="sr-only">Twitter page</span>
        </a>
    </div>
</footer>
</div>
</div>

<!-- Back to top button -->
<button id="back-to-top"
    class="fixed bottom-5 right-5 z-50 p-2 bg-blue-600 text-white rounded-full shadow-lg opacity-0 transition-opacity duration-300">
    <i class="fas fa-arrow-up"></i>
</button>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/simple-datatables@9.0.3"></script>
<script src="assets/transaction-scripts.js"></script>

<style>
    /* SweetAlert Button Styling */
    .swal-confirm-btn {
        background-color: #3085d6 !important;
        color: white !important;
        border: none !important;
        border-radius: 6px !important;
        padding: 10px 20px !important;
        font-size: 16px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        min-width: 80px !important;
        height: auto !important;
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 9999 !important;
    }

    .swal-confirm-btn:hover {
        background-color: #2563eb !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    }

    /* Ensure SweetAlert container is properly styled */
    .swal2-popup {
        z-index: 9999 !important;
        position: fixed !important;
    }

    .swal2-actions {
        display: flex !important;
        justify-content: center !important;
        margin-top: 20px !important;
    }

    .swal2-confirm {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
</style>

<?php if (isset($_SESSION['error'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: '<?php echo htmlspecialchars($_SESSION['error'], ENT_QUOTES, 'UTF-8'); ?>',
                confirmButtonText: 'OK',
                confirmButtonColor: '#dc2626',
                buttonsStyling: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                customClass: {
                    confirmButton: 'swal-confirm-btn'
                }
            });
        });
    </script>
    <?php unset($_SESSION['error']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['success'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            Swal.fire({
                icon: 'success',
                title: 'Done!',
                text: '<?php echo htmlspecialchars($_SESSION['success'], ENT_QUOTES, 'UTF-8'); ?>',
                confirmButtonText: 'OK',
                confirmButtonColor: '#16a34a',
                buttonsStyling: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                customClass: {
                    confirmButton: 'swal-confirm-btn'
                }
            });
        });
    </script>
    <?php unset($_SESSION['success']); ?>
<?php endif; ?>

<script>
    // Initialize DataTables
    document.addEventListener('DOMContentLoaded', function () {
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        if (backToTopButton) {
            window.addEventListener('scroll', function () {
                if (window.scrollY > 300) {
                    backToTopButton.classList.remove('opacity-0');
                    backToTopButton.classList.add('opacity-100');
                } else {
                    backToTopButton.classList.remove('opacity-100');
                    backToTopButton.classList.add('opacity-0');
                }
            });

            backToTopButton.addEventListener('click', function () {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    });

    // Open modal when dropdown is set to Declined
    function handleResortResponse(selectElement, bookingId) {
        const selectedValue = selectElement.value;
        if (selectedValue === 'Declined') {
            const modalElement = document.getElementById(`decline-reason-modal-${bookingId}`);
            // Use Flowbite's Modal API
            let modalInstance = window[`modalInstance_${bookingId}`];
            if (!modalInstance) {
                modalInstance = new Modal(modalElement);
                window[`modalInstance_${bookingId}`] = modalInstance;
            }
            modalInstance.show();
        }
    }

    // Reload page when any close button is clicked
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.modal-close-reload').forEach(function (btn) {
            btn.addEventListener('click', function () {
                location.reload();
            });
        });
    });

    // Enable/disable submit button and handle custom reason
    document.addEventListener('change', function (e) {
        if (e.target.name === 'decline_reason') {
            const form = e.target.closest('form');
            const bookingId = form.id.replace('declineForm-', '');
            const submitBtn = document.getElementById(`submitDeclineBtn-${bookingId}`);
            const customReasonTextarea = document.getElementById(`customDeclineReason-${bookingId}`);

            submitBtn.disabled = false;
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');

            if (e.target.value === 'other') {
                customReasonTextarea.disabled = false;
                customReasonTextarea.classList.remove('bg-gray-100', 'text-gray-500');
                customReasonTextarea.classList.add('bg-white');
                customReasonTextarea.focus();
            } else {
                customReasonTextarea.disabled = true;
                customReasonTextarea.classList.add('bg-gray-100', 'text-gray-500');
                customReasonTextarea.classList.remove('bg-white');
                customReasonTextarea.value = '';
            }
        }
    });

    document.addEventListener('input', function (e) {
        if (e.target.name === 'custom_decline_reason') {
            const form = e.target.closest('form');
            const bookingId = form.id.replace('declineForm-', '');
            const submitBtn = document.getElementById(`submitDeclineBtn-${bookingId}`);
            const otherRadio = form.querySelector('input[name="decline_reason"][value="other"]');

            if (otherRadio && otherRadio.checked) {
                if (e.target.value.trim() === '') {
                    submitBtn.disabled = true;
                    submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                } else {
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            }
        }
    });

    document.addEventListener('submit', function (e) {
        if (e.target.id && e.target.id.startsWith('declineForm-')) {
            const form = e.target;
            const selectedReason = form.querySelector('input[name="decline_reason"]:checked');

            if (!selectedReason) {
                e.preventDefault();
                alert('Please select a reason for declining this booking.');
                return false;
            }

            if (selectedReason.value === 'other') {
                const customReason = form.querySelector('textarea[name="custom_decline_reason"]').value.trim();
                if (customReason === '') {
                    e.preventDefault();
                    alert('Please provide a custom reason for declining this booking.');
                    return false;
                }
            }

            if (!confirm('Are you sure you want to decline this booking?')) {
                e.preventDefault();
                return false;
            }
        }
    });
</script>
</body>

</html>