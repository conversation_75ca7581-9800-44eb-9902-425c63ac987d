<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">

<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php" class="inline-flex items-center text-sm font-medium text-blue-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-blue-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-blue-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Pending</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-clock text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Pending Bookings</h1>
                    <p class="text-blue-100">Bookings awaiting treasurer approval</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div id="bookings-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 fade-in">
            <?php
            try {
                $getTreasurerStatus = "Pending";

                $bookingDetails = getBookingDetails($pdo, $getTreasurerStatus);

                if ($bookingDetails) {
                    foreach ($bookingDetails as $row) {
                        $booking_id = $row['booking_id'];
                        $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                        $extname = $row['extname'] ?? ''; // Use null coalescing operator for defaults
                        $middlename = $row['middlename'] ?? '';
                        $designationTour = $row['tour_operator_designation'];
                        $referenceNumber = $row['referenceNum'];

                        // Passenger and Price
                        $voucherUse = $row['voucher_use'];
                        $adultCount = $row['total_adults'] - $voucherUse;
                        $childrenCount = $row['total_children'];
                        $totalPax = $adultCount + $childrenCount;
                        $paymentMethod = $row['payment_method'];
                        $totalPrice = $row['total_amount'];
            ?>
                        <div class="booking-card bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1" data-reference="<?= htmlspecialchars($referenceNumber ?? ''); ?>" data-operator="<?= htmlspecialchars($operatorName ?? ''); ?>" data-status="pending">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($referenceNumber ?? 'N/A'); ?>
                                    </h3>
                                    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-clock mr-1"></i>
                                        Awaiting Approval
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-user-tie text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Operator:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($operatorName); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-building text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Designation:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($designationTour); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-users text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Total Pax:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= $totalPax; ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-peso-sign text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Amount:</span>
                                    <span class="ml-2 font-medium text-gray-900">₱<?= number_format($totalPrice, 2); ?></span>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex gap-2 pt-4 border-t border-gray-100">
                                <button
                                    data-modal-target="view-booking-modal-<?= $row['booking_id']; ?>"
                                    data-modal-toggle="view-booking-modal-<?= $row['booking_id']; ?>"
                                    type="button"
                                    class="action-button btn-view flex-1"
                                >
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </button>
                            </div>
                        </div>
                                <!-- View Booking Modal -->
                                <div id="view-booking-modal-<?= $row['booking_id']; ?>" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-4xl max-h-full">
                                        <!-- Modal content -->
                                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                            <!-- Modal header -->
                                            <div class="flex items-center justify-between p-4 border-b border-gray-200 rounded-t bg-gradient-to-r from-blue-600 to-blue-700">
                                                <div class="flex items-center">
                                                    <div class="bg-white bg-opacity-20 p-2 rounded-lg mr-3">
                                                        <i class="fas fa-eye text-white text-lg"></i>
                                                    </div>
                                                    <h3 class="text-xl font-semibold text-white">
                                                        Booking Details
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white bg-transparent hover:bg-white hover:bg-opacity-20 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center transition-all duration-200" data-modal-hide="view-booking-modal-<?= $row['booking_id']; ?>">
                                                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Reference Number Banner -->
                                            <div class="bg-blue-50 border-b border-blue-200 p-3">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-hashtag text-blue-600 mr-2"></i>
                                                        <span class="text-sm font-medium text-blue-800">Reference Number:</span>
                                                    </div>
                                                    <span class="text-sm font-bold text-blue-600"><?= $referenceNumber; ?></span>
                                                </div>
                                            </div>

                                            <form action="inc/inc.booking.php" method="POST">
                                                <!-- Modal body -->
                                                <div class="p-3">
                                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                                        <!-- Operator & Destination Information -->
                                                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                            <div class="flex items-center mb-2">
                                                                <i class="fas fa-user-tie text-blue-600 mr-2"></i>
                                                                <h4 class="text-sm font-semibold text-gray-800">Operator & Destination</h4>
                                                            </div>
                                                            <div class="space-y-2 text-xs">
                                                                <div class="flex justify-between">
                                                                    <span class="text-gray-600">Tour Operator:</span>
                                                                    <span class="font-medium text-gray-900"><?= $operatorName; ?></span>
                                                                </div>
                                                                <div class="flex justify-between">
                                                                    <span class="text-gray-600">Designation:</span>
                                                                    <span class="font-medium text-gray-900"><?= $designationTour; ?></span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Passenger Information -->
                                                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                            <div class="flex items-center mb-2">
                                                                <i class="fas fa-users text-blue-600 mr-2"></i>
                                                                <h4 class="text-sm font-semibold text-gray-800">Passenger Information</h4>
                                                            </div>
                                                            <div class="space-y-2 text-xs">
                                                                <div class="flex justify-between">
                                                                    <span class="text-gray-600">Adults:</span>
                                                                    <span class="font-medium text-gray-900"><?= $adultCount; ?></span>
                                                                </div>
                                                                <div class="flex justify-between">
                                                                    <span class="text-gray-600">Children:</span>
                                                                    <span class="font-medium text-gray-900"><?= $childrenCount; ?></span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Payment Information -->
                                                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
                                                            <div class="flex items-center mb-2">
                                                                <i class="fas fa-money-bill-wave text-blue-600 mr-2"></i>
                                                                <h4 class="text-sm font-semibold text-gray-800">Payment Information</h4>
                                                            </div>
                                                            <div class="space-y-2 text-xs">
                                                                <div class="flex justify-between">
                                                                    <span class="text-gray-600">Payment Method:</span>
                                                                    <span class="font-medium text-gray-900"><?= $paymentMethod; ?></span>
                                                                </div>
                                                                <div class="flex justify-between">
                                                                    <span class="text-gray-600">Total Amount:</span>
                                                                    <span class="font-bold text-lg text-green-600">₱ <?= $totalPrice; ?></span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- O.R. Number Input Section -->
                                                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-200 md:col-span-3">
                                                            <div class="flex items-center mb-2">
                                                                <i class="fas fa-receipt text-blue-600 mr-2"></i>
                                                                <h4 class="text-sm font-semibold text-gray-800">O.R. Number</h4>
                                                            </div>
                                                            <div class="relative">
                                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                    <i class="fas fa-hashtag text-gray-400"></i>
                                                                </div>
                                                                <input type="text" name="orNumber" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Enter O.R. Number" required>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Form Section -->
                                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                    <input type="hidden" name="booking_id" value="<?= $booking_id; ?>">
                                                    <input type="hidden" name="referenceNumber" value="<?= $referenceNumber; ?>">
                                                    <input type="hidden" name="treasurerResponse" value="Approved">
                                                    <input type="hidden" name="opName" value="<?= $operatorName; ?>">
                                                    <input type="hidden" name="designation" value="<?= $designationTour; ?>">
                                                    <input type="hidden" name="adultCount" value="<?= $adultCount; ?>">
                                                    <input type="hidden" name="childrenCount" value="<?= $childrenCount; ?>">
                                                    <input type="hidden" name="paymentMethod" value="<?= $paymentMethod; ?>">
                                                    <input type="hidden" name="totalAmount" value="<?= $totalPrice; ?>">
                                                </div>

                                                <!-- Modal Footer -->
                                                <div class="grid grid-cols-2 gap-2 p-3 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                                    <button data-modal-hide="view-booking-modal-<?= $row['booking_id']; ?>" type="button" class="action-button btn-close pending">
                                                        <i class="fas fa-times-circle mr-1.5"></i> Close
                                                    </button>
                                                    <button type="submit" name="treasurerResponseBtn" class="action-button btn-approve">
                                                        <i class="fas fa-check-circle mr-1.5"></i> Approve
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                        <?php
                    }
                } else {
                    // No pending bookings found
                    ?>
                    <div class="col-span-full">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
                            <div class="max-w-md mx-auto">
                                <div class="bg-gray-100 p-4 rounded-full inline-block mb-4">
                                    <i class="fas fa-inbox text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Pending Bookings</h3>
                                <p class="text-gray-500 mb-4">There are currently no bookings awaiting approval.</p>         
                            </div>                    
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="bg-white rounded-xl shadow-sm border border-red-200 p-8 text-center">
                        <div class="max-w-md mx-auto">
                            <div class="bg-red-100 p-4 rounded-full inline-block mb-4">
                                <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Bookings</h3>
                            <p class="text-gray-500 mb-4">There was an error loading the pending bookings: <?= htmlspecialchars($e->getMessage()); ?></p>
                            <button onclick="window.location.reload()" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>



<?php
require '_footer.php';
?>