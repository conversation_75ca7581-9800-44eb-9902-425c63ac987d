<?php
require '_header.php';
?>

<link rel="stylesheet" href="assets/transaction-styles.css">


<div class="p-4 sm:ml-64">
    <div class="mt-16 fade-in"> <!-- Adjusted for top navbar -->
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-orange-600 to-orange-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-orange-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-orange-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-orange-100">Transactions</span>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-orange-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-white">Canceled</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-ban text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">Cancelation Requests</h1>
                    <p class="text-orange-100">Manage booking cancellation requests</p>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-6">
            <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div class="relative flex-1 max-w-md">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search bookings..."
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors">
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-info-circle"></i>
                    <span id="bookingCount">Loading bookings...</span>
                </div>
            </div>
        </div>

        <!-- Cancellation Requests Grid -->
        <div id="bookingsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 fade-in">
            <?php
            try {
                $getBookingStatus = "request";

                $cancellationRequests = getIncompleteBooking($pdo, $getBookingStatus, $id);

                if ($cancellationRequests && count($cancellationRequests) > 0) {
                    foreach ($cancellationRequests as $row) {
                        $booking_id = $row['booking_id'] ?? $row['id'] ?? '';
                        $isCanceled = isset($row['booking_status']) && $row['booking_status'] === 'request';
                        ?>
                        <div class="booking-card bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                            data-reference="<?= htmlspecialchars($row['referenceNum'] ?? ''); ?>"
                            data-resort="<?= htmlspecialchars($row['designation'] ?? ''); ?>"
                            data-boat="<?= htmlspecialchars($row['boatName'] ?? ''); ?>" data-status="canceled">
                            <!-- Card Header -->
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                    </h3>
                                    <div
                                        class="status-badge <?= $isCanceled ? 'status-pending' : ($bothApproved ? 'status-ready' : '') ?>">
                                        <i class="fas fa-ban mr-1"></i>
                                        <?= $isCanceled ? 'Requesting Cancelation' : ($bothApproved ? 'Canceled' : '') ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Booking Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-hotel text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Resort:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-ship text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Boat:</span>
                                    <span class="ml-2 font-medium text-gray-900"><?= htmlspecialchars($row['boatName']); ?></span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-calendar text-gray-400 w-4 mr-3"></i>
                                    <span class="text-gray-600">Date Requested:</span>
                                    <span
                                        class="ml-2 font-medium text-gray-900"><?= date('M d, Y', strtotime($row['date_created'] ?? 'now')); ?></span>
                                </div>
                            </div>
                            <!-- Action Buttons -->
                            <div class="flex gap-2">
                                <button data-modal-target="approve-cancellation-modal-<?= $booking_id; ?>"
                                    data-modal-toggle="approve-cancellation-modal-<?= $booking_id; ?>" type="button"
                                    class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fas fa-check mr-1.5"></i>
                                    Approve
                                </button>
                                <button data-modal-target="reject-cancellation-modal-<?= $booking_id; ?>"
                                    data-modal-toggle="reject-cancellation-modal-<?= $booking_id; ?>" type="button"
                                    class="flex-1 inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors duration-200">
                                    <i class="fas fa-times mr-1.5"></i>
                                    Reject
                                </button>
                            </div>
                        </div>

                        <!-- Approve Cancellation Modal -->
                        <div id="approve-cancellation-modal-<?= $booking_id; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full backdrop-blur-sm bg-gray-900/50">
                            <div class="relative w-full max-w-md max-h-full">
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-4 bg-gradient-to-r from-green-600 to-green-700 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-check-circle text-white mr-2"></i>
                                            <h3 class="text-xl font-semibold text-white">
                                                Approve Cancellation
                                            </h3>
                                        </div>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-green-800 hover:bg-green-900"
                                            data-modal-hide="approve-cancellation-modal-<?= $booking_id; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>

                                    <!-- Modal Body -->
                                    <form action="inc/inc.cancellation.php" method="POST">
                                        <div class="p-5 bg-white rounded-lg space-y-4">
                                            <!-- Confirmation Message -->
                                            <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                                <p class="text-sm text-green-800">
                                                    <i class="fas fa-info-circle mr-2"></i>
                                                    Are you sure you want to approve this cancellation request?
                                                </p>
                                                <p class="text-xs text-green-600 mt-2">
                                                    This will cancel the booking and notify the tour operator.
                                                </p>
                                            </div>

                                            <!-- Booking Reference -->
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                                                <p class="text-xs text-gray-500 uppercase font-medium">Reference Number</p>
                                                <p class="text-sm font-semibold text-gray-800 mt-1">
                                                    <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Hidden Form Fields -->
                                        <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token']; ?>">
                                        <input type="hidden" name="booking_id" value="<?= $booking_id; ?>">
                                        <input type="hidden" name="action" value="approve">

                                        <!-- Modal Footer -->
                                        <div
                                            class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg space-x-3">
                                            <button data-modal-hide="approve-cancellation-modal-<?= $booking_id; ?>" type="button"
                                                class="py-2 px-4 text-sm font-semibold text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                <i class="fas fa-times mr-1"></i> Cancel
                                            </button>
                                            <button type="submit" name="cancellationAction"
                                                class="py-2 px-4 text-sm font-semibold text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-200">
                                                <i class="fas fa-check mr-1"></i> Approve Cancellation
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Reject Cancellation Modal -->
                        <div id="reject-cancellation-modal-<?= $booking_id; ?>" tabindex="-1"
                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full backdrop-blur-sm bg-gray-900/50">
                            <div class="relative w-full max-w-lg max-h-full">
                                <!-- Modal Container -->
                                <div class="relative bg-white rounded-lg shadow-lg">
                                    <!-- Modal Header -->
                                    <div
                                        class="flex items-center justify-between p-4 bg-gradient-to-r from-red-600 to-red-700 rounded-t-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-times-circle text-white mr-2"></i>
                                            <h3 class="text-xl font-semibold text-white">
                                                Reject Cancellation
                                            </h3>
                                        </div>
                                        <button type="button"
                                            class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-red-800 hover:bg-red-900"
                                            data-modal-hide="reject-cancellation-modal-<?= $booking_id; ?>">
                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                                viewBox="0 0 14 14">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                    stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                            </svg>
                                            <span class="sr-only">Close modal</span>
                                        </button>
                                    </div>

                                    <!-- Modal Body -->
                                    <form action="inc/inc.cancellation.php" method="POST"
                                        id="rejectCancellationForm-<?= $booking_id; ?>">
                                        <div class="p-5 bg-white rounded-lg space-y-4">
                                            <!-- Admin Section -->
                                            <div class="bg-gray-800 text-white p-4 rounded-lg">
                                                <h4 class="text-lg font-semibold mb-2">Admin:</h4>
                                                <span>Must give a reason for rejecting the cancellation request.</span>

                                                <!-- Choose reason or write your own -->
                                                <div class="flex items-center my-4 mb-2">
                                                    <span class="text-sm">Choose a reason or write your own:</span>
                                                </div>

                                                <!-- Predefined Reasons -->
                                                <div class="space-y-2 mb-4">
                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="reject_reason" value="Insufficient documentation"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Insufficient documentation</span>
                                                    </label>

                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="reject_reason" value="Policy violation"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Policy violation</span>
                                                    </label>

                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="reject_reason" value="Too close to travel date"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Too close to travel date</span>
                                                    </label>

                                                    <label class="flex items-center cursor-pointer">
                                                        <div class="w-2 h-2 bg-pink-400 rounded-full mr-3"></div>
                                                        <input type="radio" name="reject_reason" value="other"
                                                            class="mr-2 text-pink-400 focus:ring-pink-400">
                                                        <span class="text-sm">Other: [Text box]</span>
                                                    </label>
                                                </div>

                                                <!-- Custom Reason Text Box -->
                                                <div class="mt-3">
                                                    <textarea name="custom_reject_reason"
                                                        id="customRejectReason-<?= $booking_id; ?>"
                                                        placeholder="Please specify your reason..."
                                                        class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-400 focus:border-pink-400 text-gray-900 text-sm"
                                                        rows="3" disabled></textarea>
                                                </div>
                                            </div>

                                            <!-- Booking Reference -->
                                            <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                                                <p class="text-xs text-gray-500 uppercase font-medium">Reference Number</p>
                                                <p class="text-sm font-semibold text-gray-800 mt-1">
                                                    <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Hidden Form Fields -->
                                        <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token']; ?>">
                                        <input type="hidden" name="booking_id" value="<?= $booking_id; ?>">
                                        <input type="hidden" name="action" value="reject">

                                        <!-- Modal Footer -->
                                        <div
                                            class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg space-x-3">
                                            <button data-modal-hide="reject-cancellation-modal-<?= $booking_id; ?>" type="button"
                                                class="py-2 px-4 text-sm font-semibold text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                <i class="fas fa-times mr-1"></i> Cancel
                                            </button>
                                            <button type="submit" name="cancellationAction" id="submitRejectBtn-<?= $booking_id; ?>"
                                                class="py-2 px-4 text-sm font-semibold text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200 opacity-50 cursor-not-allowed"
                                                disabled>
                                                <i class="fas fa-times mr-1"></i> Reject Cancellation
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <?php
                    }
                } else {
                    // No cancellation requests found
                    ?>
                    <div class="col-span-full">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
                            <div class="max-w-md mx-auto">
                                <div class="bg-orange-100 p-4 rounded-full inline-block mb-4">
                                    <i class="fas fa-inbox text-orange-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No Cancellation Requests</h3>
                                <p class="text-gray-500 mb-4">When cancellation requests are submitted, they will appear here.
                                </p>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } catch (PDOException $e) {
                ?>
                <div class="col-span-full">
                    <div class="bg-white rounded-xl shadow-sm border border-red-200 p-8 text-center">
                        <div class="max-w-md mx-auto">
                            <div class="bg-red-100 p-4 rounded-full inline-block mb-4">
                                <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Loading Requests</h3>
                            <p class="text-gray-500 mb-4">There was an error loading the cancelation requests:
                                <?= htmlspecialchars($e->getMessage()); ?>
                            </p>
                            <button onclick="window.location.reload()"
                                class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors">
                                <i class="fas fa-refresh mr-2"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </div>

    <script>
        // Handle reject reason selection
        document.addEventListener('change', function (e) {
            if (e.target.name === 'reject_reason') {
                const form = e.target.closest('form');
                const bookingId = form.id.replace('rejectCancellationForm-', '');
                const submitBtn = document.getElementById(`submitRejectBtn-${bookingId}`);
                const customReasonTextarea = document.getElementById(`customRejectReason-${bookingId}`);

                // Enable submit button when any reason is selected
                submitBtn.disabled = false;
                submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');

                // Handle "other" option
                if (e.target.value === 'other') {
                    customReasonTextarea.disabled = false;
                    customReasonTextarea.classList.remove('bg-gray-100', 'text-gray-500');
                    customReasonTextarea.classList.add('bg-white');
                    customReasonTextarea.focus();
                } else {
                    customReasonTextarea.disabled = true;
                    customReasonTextarea.classList.add('bg-gray-100', 'text-gray-500');
                    customReasonTextarea.classList.remove('bg-white');
                    customReasonTextarea.value = '';
                }
            }
        });

        // Handle custom reason textarea input
        document.addEventListener('input', function (e) {
            if (e.target.name === 'custom_reject_reason') {
                const form = e.target.closest('form');
                const bookingId = form.id.replace('rejectCancellationForm-', '');
                const submitBtn = document.getElementById(`submitRejectBtn-${bookingId}`);
                const otherRadio = form.querySelector('input[name="reject_reason"][value="other"]');

                if (otherRadio && otherRadio.checked) {
                    if (e.target.value.trim() === '') {
                        submitBtn.disabled = true;
                        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                    } else {
                        submitBtn.disabled = false;
                        submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                }
            }
        });

        // Handle form submission validation
        document.addEventListener('submit', function (e) {
            if (e.target.id && e.target.id.startsWith('rejectCancellationForm-')) {
                const form = e.target;
                const selectedReason = form.querySelector('input[name="reject_reason"]:checked');

                if (!selectedReason) {
                    e.preventDefault();
                    alert('Please select a reason for rejecting this cancellation request.');
                    return false;
                }

                if (selectedReason.value === 'other') {
                    const customReason = form.querySelector('textarea[name="custom_reject_reason"]').value.trim();
                    if (customReason === '') {
                        e.preventDefault();
                        alert('Please provide a custom reason for rejecting this cancellation request.');
                        return false;
                    }
                }

                // Confirmation prompt
                if (!confirm('Are you sure you want to reject this cancellation request?')) {
                    e.preventDefault();
                    return false;
                }
            }
        });

        // Handle approve cancellation confirmation
        document.addEventListener('submit', function (e) {
            if (e.target.querySelector('input[name="action"][value="approve"]')) {
                if (!confirm('Are you sure you want to approve this cancellation request? This action cannot be undone.')) {
                    e.preventDefault();
                    return false;
                }
            }
        });

        // Reset modal state when closed
        document.addEventListener('click', function (e) {
            if (e.target.hasAttribute('data-modal-hide') && e.target.getAttribute('data-modal-hide').includes('reject-cancellation-modal')) {
                const modalId = e.target.getAttribute('data-modal-hide');
                const bookingId = modalId.replace('reject-cancellation-modal-', '');

                // Reset form
                const form = document.getElementById(`rejectCancellationForm-${bookingId}`);
                if (form) {
                    form.reset();

                    // Reset submit button
                    const submitBtn = document.getElementById(`submitRejectBtn-${bookingId}`);
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                    }

                    // Reset custom reason textarea
                    const customReasonTextarea = document.getElementById(`customRejectReason-${bookingId}`);
                    if (customReasonTextarea) {
                        customReasonTextarea.disabled = true;
                        customReasonTextarea.classList.add('bg-gray-100', 'text-gray-500');
                        customReasonTextarea.classList.remove('bg-white');
                        customReasonTextarea.value = '';
                    }
                }
            }
        });
    </script>

    <?php
    require '_footer.php';
    ?>