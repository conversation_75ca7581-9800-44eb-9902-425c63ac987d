<?php
require '_header.php';

// Get booking statistics
$draftBookings = $notif['draft'] ?? 0;
$pendingBookings = $notif['pending'] ?? 0;
$approvedBookings = $notif['approved'] ?? 0;
$completedBookings = $notif['completed'] ?? 0;
$totalBookings = $draftBookings + $pendingBookings + $approvedBookings + $completedBookings;

// Get voucher counts
$voucherVinzons = 0;
$voucherOthers = 0;
try {
    $voucherSql = "SELECT voucher_vinzons, voucher_others FROM cb_vouchers WHERE operator_id = :operator_id";
    $voucherStmt = $pdo->prepare($voucherSql);
    $voucherStmt->bindParam(':operator_id', $id, PDO::PARAM_INT);
    $voucherStmt->execute();
    $voucherInfo = $voucherStmt->fetch(PDO::FETCH_ASSOC);

    if ($voucherInfo) {
        $voucherVinzons = (int) $voucherInfo['voucher_vinzons'];
        $voucherOthers = (int) $voucherInfo['voucher_others'];
    }
} catch (PDOException $e) {
    // Keep default values
}
$totalVouchers = $voucherVinzons + $voucherOthers;

// Get recent bookings (limit to 5)
try {
    $sql = "SELECT
                bb.booking_id,
                bb.referenceNum,
                bb.booking_status,
                bb.check_in_date,
                bb.check_out_date,
                oi.designation AS resort_name,
                bob.boatName
            FROM cb_bookings bb
            LEFT JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
            LEFT JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
            WHERE bb.tour_operator_id = :tour_operator_id
            ORDER BY bb.booking_id DESC
            LIMIT 5";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':tour_operator_id', $id, PDO::PARAM_INT);
    $stmt->execute();
    $recentBookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $recentBookings = [];
}

// Sample data for chart (monthly bookings)
$monthlyData = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
$monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

try {
    $sql = "SELECT
                MONTH(date_created) as month,
                COUNT(*) as count
            FROM cb_bookings
            WHERE tour_operator_id = :tour_operator_id
            AND YEAR(date_created) = YEAR(CURRENT_DATE())
            GROUP BY MONTH(date_created)";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':tour_operator_id', $id, PDO::PARAM_INT);
    $stmt->execute();
    $monthlyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($monthlyStats as $stat) {
        $monthIndex = $stat['month'] - 1; // Adjust for 0-based array
        $monthlyData[$monthIndex] = (int) $stat['count'];
    }
} catch (PDOException $e) {
    // Keep default empty data
}
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Welcome Section -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-700 rounded-lg shadow-lg p-6 mb-6 text-white">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Welcome back, <?= explode(' ', $nameEscaped)[0]; ?>!</h1>
                    <p class="mt-1 text-blue-100">Here's what's happening with your tour operations today.</p>
                </div>
                <div class="hidden md:block">
                    <span class="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">
                        <?= date('l, F j, Y'); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Total Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Total Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $totalBookings ?></h3>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                </div>
            </div>
            <!-- Draft Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-gray-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Draft Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $draftBookings ?></h3>
                    </div>
                    <div class="p-3 bg-gray-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="booking-draft.php" class="text-gray-600 text-sm font-medium hover:underline flex items-center">
                        View drafts
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Pending Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Pending Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $pendingBookings ?></h3>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="booking-pending.php" class="text-yellow-600 text-sm font-medium hover:underline flex items-center">
                        View pending
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Approved Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Approved Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $approvedBookings ?></h3>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="booking-approved.php" class="text-green-600 text-sm font-medium hover:underline flex items-center">
                        View approved
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        <!-- Voucher Details Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center mb-4">
                <div class="p-2 bg-blue-100 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-800">Your Available Vouchers</h3>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Vinzons Port Vouchers -->
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 border border-blue-200">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-500 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-blue-800">Vinzons Port Vouchers</h4>
                        </div>
                    </div>
                    <div class="text-3xl font-bold text-blue-600 mb-2"><?= $voucherVinzons ?> <span class="text-lg font-normal text-blue-500">available</span></div>
                    <p class="text-sm text-blue-600 mb-4">Valid for trips departing from Vinzons Port</p>
                    <?php if ($voucherVinzons > 0): ?>
                            <a href="booking-voucher.php" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Use Voucher
                            </a>
                    <?php else: ?>
                            <span class="inline-flex items-center px-4 py-2 bg-gray-300 text-gray-500 text-sm font-medium rounded-md cursor-not-allowed">
                                No vouchers available
                            </span>
                    <?php endif; ?>
                </div>

                <!-- Other Ports Vouchers -->
                <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 border border-green-200">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-500 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-green-800">Other Ports Vouchers</h4>
                        </div>
                    </div>
                    <div class="text-3xl font-bold text-green-600 mb-2"><?= $voucherOthers ?> <span class="text-lg font-normal text-green-500">available</span></div>
                    <p class="text-sm text-green-600 mb-4">Valid for trips departing from other ports</p>
                    <?php if ($voucherOthers > 0): ?>
                            <a href="booking-voucher.php" class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Use Voucher
                            </a>
                    <?php else: ?>
                            <span class="inline-flex items-center px-4 py-2 bg-gray-300 text-gray-500 text-sm font-medium rounded-md cursor-not-allowed">
                                No vouchers available
                            </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Chart and Recent Bookings -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Chart -->
            <div class="bg-white rounded-lg shadow-md p-6 lg:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Booking Trends</h3>
                    <div class="text-sm text-gray-500">Monthly statistics for <?= date('Y') ?></div>
                </div>
                <div>
                    <canvas id="bookingChart" height="300"></canvas>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                <div class="grid grid-cols-2 gap-3">
                    <a href="booking-create.php" class="flex flex-col items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                        <div class="p-2 bg-blue-100 rounded-full mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </div>
                        <span class="text-xs font-medium text-gray-700">New Booking</span>
                    </a>
                    <a href="booking-pending.php" class="flex flex-col items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                        <div class="p-2 bg-yellow-100 rounded-full mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                        </div>
                        <span class="text-xs font-medium text-gray-700">Pending</span>
                    </a>
                    <a href="booking-approved.php" class="flex flex-col items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                        <div class="p-2 bg-green-100 rounded-full mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <span class="text-xs font-medium text-gray-700">Approved</span>
                    </a>
                    <a href="booking-completed.php" class="flex flex-col items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                        <div class="p-2 bg-purple-100 rounded-full mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <span class="text-xs font-medium text-gray-700">Completed</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Bookings Table -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Recent Bookings</h3>
                <a href="booking-all.php" class="text-sm font-medium text-blue-600 hover:underline">View all</a>
            </div>

            <?php if (empty($recentBookings)): ?>
                    <div class="text-center py-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        <p class="mt-2 text-gray-500">No bookings found</p>
                        <a href="booking-create.php" class="mt-3 inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Create New Booking
                        </a>
                    </div>
            <?php else: ?>
                    <div class="overflow-x-auto">
                        <table id="search-table" class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference #</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resort</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Boat</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-in</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($recentBookings as $booking): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($booking['referenceNum']) ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?= htmlspecialchars($booking['resort_name'] ?? 'N/A') ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?= htmlspecialchars($booking['boatName'] ?? 'N/A') ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?= date('M d, Y', strtotime($booking['check_in_date'])) ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php
                                                $statusClass = '';
                                                $statusText = ucfirst($booking['booking_status']);

                                                switch ($booking['booking_status']) {
                                                    case 'draft':
                                                        $statusClass = 'bg-gray-100 text-gray-800';
                                                        break;
                                                    case 'pending':
                                                        $statusClass = 'bg-yellow-100 text-yellow-800';
                                                        break;
                                                    case 'approved':
                                                        $statusClass = 'bg-green-100 text-green-800';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'bg-blue-100 text-blue-800';
                                                        break;
                                                    case 'declined':
                                                        $statusClass = 'bg-red-100 text-red-800';
                                                        break;
                                                    default:
                                                        $statusClass = 'bg-gray-100 text-gray-800';
                                                }
                                                ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $statusClass ?>">
                                                    <?= $statusText ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <a href="booking-view.php?id=<?= $booking['booking_id'] ?>" class="text-blue-600 hover:text-blue-900">View</a>
                                            </td>
                                        </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
            <?php endif; ?>
        </div>
    </div>


<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Initialize chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('bookingChart').getContext('2d');
    const bookingChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: <?= json_encode($monthNames) ?>,
            datasets: [{
                label: 'Bookings',
                data: <?= json_encode($monthlyData) ?>,
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false,
                        color: 'rgba(200, 200, 200, 0.2)'
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    padding: 10,
                    titleFont: {
                        size: 14
                    },
                    bodyFont: {
                        size: 13
                    },
                    displayColors: false
                }
            }
        }
    });
});
</script>

<?php
require '_footer.php';
?>