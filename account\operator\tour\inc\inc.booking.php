<?php
session_start([
    'cookie_secure' => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);
include '../../../../connection/dbconnect.php';
include 'inc.function.php';
if ($_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
} else {
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        // Handle updating declined bookings
        if (isset($_POST['updateDeclinedBookingBtn'])) {
            try {
                if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                    throw new Exception("Invalid CSRF token.");
                }
                if (!isset($_SESSION['id'])) {
                    throw new Exception("User not authenticated");
                }

                // Validate Booking ID
                $bookingId = $_POST['booking_id'] ?? null;
                if (!$bookingId) {
                    throw new Exception("Invalid booking ID.");
                }

                $referenceNumber = trim($_POST['referenceNumber']);

                // Get the booking details to check who declined it and if it's a voucher booking
                $stmt = $pdo->prepare("
                    SELECT
                        b.booking_id,
                        b.booking_status,
                        ba.resort,
                        ba.boat,
                        cp.payment_status,
                        CASE
                            WHEN ba.resort = 'Declined' THEN 'Resort Operator'
                            WHEN ba.boat = 'Declined' THEN 'Boat Operator'
                            WHEN ba.mtho = 'Declined' THEN 'MTHO'
                            ELSE 'Unknown'
                        END AS declined_by
                    FROM cb_bookings b
                    JOIN cb_booking_approvals ba ON b.booking_id = ba.booking_id
                    LEFT JOIN cb_payments cp ON b.booking_id = cp.booking_id
                    WHERE b.booking_id = :booking_id
                    AND b.tour_operator_id = :tour_operator_id
                    AND b.booking_status = 'declined'
                ");
                $stmt->execute([
                    ':booking_id' => $bookingId,
                    ':tour_operator_id' => $_SESSION['id']
                ]);
                $booking = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$booking) {
                    throw new Exception("Booking not found or not declined.");
                }

                // Check if this is a voucher booking
                $isVoucherBooking = ($booking['payment_status'] === 'voucher');

                // Get and validate dates
                $checkinDate = $_POST['checkin'] ?? '';
                $checkoutDate = $_POST['checkout'] ?? '';

                if (empty($checkinDate) || empty($checkoutDate)) {
                    throw new Exception("Check-in and check-out dates are required.");
                }

                // Validate date format and logic
                $checkin = new DateTime($checkinDate);
                $checkout = new DateTime($checkoutDate);

                if ($checkout <= $checkin) {
                    throw new Exception("Check-out date must be after check-in date.");
                }

                // Start transaction
                $pdo->beginTransaction();

                // Always update the dates for declined bookings
                $stmt = $pdo->prepare("UPDATE cb_bookings SET check_in_date = :checkin, check_out_date = :checkout WHERE booking_id = :booking_id");
                $stmt->execute([
                    ':checkin' => $checkinDate,
                    ':checkout' => $checkoutDate,
                    ':booking_id' => $bookingId
                ]);

                // Update based on who declined
                if ($booking['declined_by'] === 'Resort Operator') {
                    // Always update the resort
                    $resortId = (int) $_POST['resort'];
                    if (!$resortId) {
                        throw new Exception("Invalid resort selection.");
                    }

                    // For voucher bookings, port cannot be changed as it's tied to voucher type
                    if ($isVoucherBooking) {
                        // Only update resort for voucher bookings
                        $stmt = $pdo->prepare("UPDATE cb_bookings SET resort_operator_id = :resort_id WHERE booking_id = :booking_id");
                        $stmt->execute([
                            ':resort_id' => $resortId,
                            ':booking_id' => $bookingId
                        ]);

                        error_log("Voucher booking - Updated resort only. Booking ID: " . $bookingId . ", New resort ID: " . $resortId);
                    } else {
                        // For regular bookings, update both port and resort
                        $portId = (int) $_POST['port'];
                        if (!$portId) {
                            throw new Exception("Invalid port selection.");
                        }

                        $stmt = $pdo->prepare("UPDATE cb_bookings SET port_id = :port_id, resort_operator_id = :resort_id WHERE booking_id = :booking_id");
                        $stmt->execute([
                            ':port_id' => $portId,
                            ':resort_id' => $resortId,
                            ':booking_id' => $bookingId
                        ]);

                        error_log("Regular booking - Updated both port and resort. Booking ID: " . $bookingId . ", New port ID: " . $portId . ", New resort ID: " . $resortId);
                    }

                    // Reset the resort approval status to Pending
                    $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET resort = 'Pending' WHERE booking_id = :booking_id");
                    $stmt->execute([
                        ':booking_id' => $bookingId
                    ]);

                } elseif ($booking['declined_by'] === 'Boat Operator') {
                    // If declined by boat, update the boat
                    $boatId = (int) $_POST['boat'];
                    if (!$boatId) {
                        throw new Exception("Invalid boat selection.");
                    }

                    $stmt = $pdo->prepare("UPDATE cb_bookings SET boat_id = :boat_id WHERE booking_id = :booking_id");
                    $stmt->execute([
                        ':boat_id' => $boatId,
                        ':booking_id' => $bookingId
                    ]);

                    // For voucher bookings, port cannot be changed as it's tied to voucher type
                    if ($isVoucherBooking) {

                        // Only update boat for voucher bookings
                        $stmt = $pdo->prepare("UPDATE cb_bookings SET boat_id = :boat_id WHERE booking_id = :booking_id");
                        $stmt->execute([
                            ':boat_id' => $boatId,
                            ':booking_id' => $bookingId
                        ]);

                        error_log("Voucher booking - Updated resort only. Booking ID: " . $bookingId . ", New resort ID: " . $resortId);
                    } else {
                        // For regular bookings, update both port and resort
                        $portId = (int) $_POST['port'];
                        if (!$portId) {
                            throw new Exception("Invalid port selection.");
                        }

                        $stmt = $pdo->prepare("UPDATE cb_bookings SET port_id = :port_id, boat_id = :boat_id WHERE booking_id = :booking_id");
                        $stmt->execute([
                            ':port_id' => $portId,
                            ':boat_id' => $boatId,
                            ':booking_id' => $bookingId
                        ]);

                        error_log("Regular booking - Updated both port and boat. Booking ID: " . $bookingId . ", New port ID: " . $portId . ", New Boat ID: " . $boat_id);
                    }

                    // Reset the boat approval status to Pending
                    $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET boat = 'Pending' WHERE booking_id = :booking_id");
                    $stmt->execute([
                        ':booking_id' => $bookingId
                    ]);
                } elseif ($booking['declined_by'] === 'MTHO') {
                    // If declined by MTHO, update resort, boat, and conditionally port
                    $resortId = (int) $_POST['resort'];
                    $boatId = (int) $_POST['boat'];

                    if (!$resortId) {
                        throw new Exception("Invalid resort selection.");
                    }
                    if (!$boatId) {
                        throw new Exception("Invalid boat selection.");
                    }

                    // For voucher bookings, port cannot be changed as it's tied to voucher type
                    if ($isVoucherBooking) {
                        // Only update resort and boat for voucher bookings
                        $stmt = $pdo->prepare("UPDATE cb_bookings SET resort_operator_id = :resort_id, boat_id = :boat_id WHERE booking_id = :booking_id");
                        $stmt->execute([
                            ':resort_id' => $resortId,
                            ':boat_id' => $boatId,
                            ':booking_id' => $bookingId
                        ]);

                        error_log("Voucher booking - Updated resort and boat only. Booking ID: " . $bookingId . ", New resort ID: " . $resortId . ", New boat ID: " . $boatId);
                    } else {
                        // For regular bookings, update port, resort, and boat
                        $portId = (int) $_POST['port'];
                        if (!$portId) {
                            throw new Exception("Invalid port selection.");
                        }

                        $stmt = $pdo->prepare("UPDATE cb_bookings SET port_id = :port_id, resort_operator_id = :resort_id, boat_id = :boat_id WHERE booking_id = :booking_id");
                        $stmt->execute([
                            ':port_id' => $portId,
                            ':resort_id' => $resortId,
                            ':boat_id' => $boatId,
                            ':booking_id' => $bookingId
                        ]);

                        error_log("Regular booking - Updated port, resort, and boat. Booking ID: " . $bookingId . ", New port ID: " . $portId . ", New resort ID: " . $resortId . ", New boat ID: " . $boatId);
                    }

                    // Reset all approval statuses to Pending
                    $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET resort = 'Pending', boat = 'Pending', mtho = 'Pending' WHERE booking_id = :booking_id");
                    $stmt->execute([
                        ':booking_id' => $bookingId
                    ]);
                } else {
                    throw new Exception("Unable to determine who declined this booking.");
                }

                // Get tourist counts for payment update
                $counts = getTouristAndCrewCounts($pdo, $bookingId);
                $adults = $counts['adults'] ?? 0;
                $children = $counts['children'] ?? 0;
                $crew = $counts['crewTts'] ?? 0;

                try {
                    // Get port fee from database
                    $feeStmt = $pdo->prepare("SELECT fee FROM port_list WHERE id = (SELECT port_id FROM cb_bookings WHERE booking_id = :booking_id)");
                    $feeStmt->execute([':booking_id' => $bookingId]);
                    $feeResult = $feeStmt->fetch(PDO::FETCH_ASSOC);

                    if (!$feeResult) {
                        throw new Exception("Could not retrieve port fee information.");
                    }

                    $fee = $feeResult['fee'] ?? 0;

                    // Calculate total amount
                    $totalAmt = $adults * $fee;

                    // Update payment information based on booking type
                    if ($isVoucherBooking) {
                        // For voucher bookings, don't change payment_status or amount calculation
                        $paymentStmt = $pdo->prepare("
                            UPDATE cb_payments SET
                            total_adults = :adults,
                            total_children = :children,
                            total_crew = :crew
                            WHERE booking_id = :booking_id
                        ");
                        $paymentStmt->execute([
                            ':adults' => $adults,
                            ':children' => $children,
                            ':crew' => $crew,
                            ':booking_id' => $bookingId
                        ]);

                        error_log("Voucher booking payment updated - no amount change. Booking ID: " . $bookingId);
                    } else {
                        // For regular bookings, update all payment fields including amount
                        $paymentStmt = $pdo->prepare("
                            UPDATE cb_payments SET
                            total_adults = :adults,
                            total_children = :children,
                            total_crew = :crew,
                            total_amount = :amt,
                            payment_status = 'unpaid'
                            WHERE booking_id = :booking_id
                        ");
                        $paymentStmt->execute([
                            ':adults' => $adults,
                            ':children' => $children,
                            ':crew' => $crew,
                            ':amt' => $totalAmt,
                            ':booking_id' => $bookingId
                        ]);

                        error_log("Regular booking payment updated with new amount: " . $totalAmt . ". Booking ID: " . $bookingId);
                    }
                } catch (Exception $paymentError) {
                    error_log("Error updating payment: " . $paymentError->getMessage());
                    // Continue with the booking update even if payment update fails
                }

                // Update booking status back to pending
                $stmt = $pdo->prepare("UPDATE cb_bookings SET booking_status = 'pending' WHERE booking_id = :booking_id");
                $stmt->execute([
                    ':booking_id' => $bookingId
                ]);

                // Insert log entry
                $type = "Booking - Updated After Decline";
                $description = "Tour operator: " . $_SESSION['username'] . " updated booking: " . $referenceNumber . " after it was declined by " . $booking['declined_by'] . ". Date: " . date("Y-m-d");
                $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
                $stmt->execute([
                    ':type' => $type,
                    ':description' => $description
                ]);

                // Commit transaction
                $pdo->commit();

                $_SESSION['success'] = "Booking updated successfully and is now pending approval.";
                header("Location: ../transaction-pending.php");
                exit();
            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                $_SESSION['error'] = $e->getMessage();
                header("Location: ../transaction-declined.php");
                exit();
            }
        }

        if (isset($_POST['bookingBtn'])) {
            try {
                // Start transaction
                $pdo->beginTransaction();

                if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                    throw new Exception("Invalid CSRF token.");
                }
                if (!isset($_SESSION['id'])) {
                    throw new Exception("User not authenticated");
                }

                $tourOperatorId = $_SESSION['id'];
                $resortOperatorId = filter_var($_POST['resort'], FILTER_VALIDATE_INT);
                $boatId = filter_var($_POST['boat'], FILTER_VALIDATE_INT);
                $portId = filter_var($_POST['port'], FILTER_VALIDATE_INT);
                $checkIn = htmlspecialchars($_POST['checkin']);
                $checkOut = htmlspecialchars($_POST['checkout']);
                $adult = filter_var($_POST['adult'], FILTER_VALIDATE_INT);
                $children = filter_var($_POST['children'], FILTER_VALIDATE_INT);

                // Check if this is a voucher booking
                $isVoucherBooking = isset($_POST['voucher_booking']) && $_POST['voucher_booking'] == 1;
                $voucherType = $isVoucherBooking ? $_POST['voucher_type'] : null;

                // If this is a voucher booking, validate the voucher
                if ($isVoucherBooking) {
                    if (!in_array($voucherType, ['vinzons', 'others'])) {
                        throw new Exception("Invalid voucher type selected.");
                    }

                    // Get port name to validate voucher type
                    $portStmt = $pdo->prepare("SELECT portName FROM port_list WHERE id = :id");
                    $portStmt->execute([':id' => $portId]);
                    $portInfo = $portStmt->fetch(PDO::FETCH_ASSOC);
                    $portName = $portInfo['portName'] ?? '';

                    // Check if port name matches voucher type
                    $isVinzonsPort = stripos($portName, 'vinzons') !== false;

                    if ($voucherType === 'vinzons' && !$isVinzonsPort) {
                        throw new Exception("Vinzons vouchers can only be used for Vinzons port.");
                    }

                    if ($voucherType === 'others' && $isVinzonsPort) {
                        throw new Exception("Other vouchers cannot be used for Vinzons port.");
                    }

                    // Check if operator has available vouchers
                    $voucherStmt = $pdo->prepare("SELECT voucher_vinzons, voucher_others FROM cb_vouchers WHERE operator_id = :operator_id");
                    $voucherStmt->execute([':operator_id' => $tourOperatorId]);
                    $voucherInfo = $voucherStmt->fetch(PDO::FETCH_ASSOC);

                    if (!$voucherInfo) {
                        throw new Exception("No vouchers found for this operator.");
                    }

                    if ($voucherType === 'vinzons' && (int) $voucherInfo['voucher_vinzons'] <= 0) {
                        throw new Exception("No Vinzons vouchers available.");
                    }

                    if ($voucherType === 'others' && (int) $voucherInfo['voucher_others'] <= 0) {
                        throw new Exception("No other vouchers available.");
                    }
                }

                // Convert dates to DateTime objects for proper comparison
                $checkInDate = new DateTime($checkIn);
                $checkOutDate = new DateTime($checkOut);

                if ($checkOutDate < $checkInDate) {
                    throw new Exception("Check-out date must be after check-in date.");
                }

                // Generate unique reference number
                $year = date('Y'); // Get current year
                $randomPart1 = strtoupper(substr(bin2hex(random_bytes(3)), 0, 6)); // 6 random characters
                $randomPart2 = strtoupper(substr(bin2hex(random_bytes(2)), 0, 4)); // 4 random characters

                // Format the reference number
                $referenceNumber = "CB-$year-$randomPart1-$randomPart2";

                // Get port Fee for payment
                $stmt = $pdo->prepare("SELECT fee FROM port_list WHERE id = :id");
                $stmt->execute(['id' => $portId]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $fee = $result['fee'];

                // Determine booking status based on voucher usage
                $bookingStatus = $isVoucherBooking ? 'voucher' : 'draft';

                // Insert booking
                $sql = "INSERT INTO cb_bookings (
                        referenceNum,
                        tour_operator_id,
                        resort_operator_id,
                        boat_id,
                        port_id,
                        check_in_date,
                        check_out_date,
                        booking_status,
                        date_created
                    ) VALUES (
                        :referenceNumber,
                        :tour_operator_id,
                        :resort_operator_id,
                        :boat_id,
                        :port_id,
                        :check_in_date,
                        :check_out_date,
                        :booking_status,
                        NOW()
                    )";

                $stmt = $pdo->prepare($sql);
                $stmt->bindParam(':referenceNumber', $referenceNumber, PDO::PARAM_STR);
                $stmt->bindParam(':tour_operator_id', $tourOperatorId, PDO::PARAM_INT);
                $stmt->bindParam(':resort_operator_id', $resortOperatorId, PDO::PARAM_INT);
                $stmt->bindParam(':boat_id', $boatId, PDO::PARAM_INT);
                $stmt->bindParam(':port_id', $portId, PDO::PARAM_INT);
                $stmt->bindParam(':check_in_date', $checkIn);
                $stmt->bindParam(':check_out_date', $checkOut);
                $stmt->bindParam(':booking_status', $bookingStatus, PDO::PARAM_STR);

                $stmt->execute();
                $bookingId = $pdo->lastInsertId();

                // Insert payment entry with voucher_use flag if applicable
                if ($isVoucherBooking) {
                    $stmt = $pdo->prepare("INSERT INTO cb_payments (booking_id, total_adults, total_children, payment_status, port_fee, voucher_use)
                    VALUES (:booking_id, :adult, :children, 'voucher', :portFee, 0)");
                } else {
                    $stmt = $pdo->prepare("INSERT INTO cb_payments (booking_id, total_adults, total_children, payment_status, port_fee)
                    VALUES (:booking_id, :adult, :children, 'unpaid', :portFee)");
                }

                $stmt->execute([
                    ':booking_id' => $bookingId,
                    ':adult' => $adult,
                    ':children' => $children,
                    ':portFee' => $fee
                ]);

                // If this is a voucher booking, deduct the voucher
                if ($isVoucherBooking) {
                    if ($voucherType === 'vinzons') {
                        $updateVoucherStmt = $pdo->prepare("UPDATE cb_vouchers SET voucher_vinzons = voucher_vinzons - 1 WHERE operator_id = :operator_id");
                    } else {
                        $updateVoucherStmt = $pdo->prepare("UPDATE cb_vouchers SET voucher_others = voucher_others - 1 WHERE operator_id = :operator_id");
                    }

                    $updateVoucherStmt->execute([':operator_id' => $tourOperatorId]);
                }

                // Commit transaction
                $pdo->commit();

                // Redirect to next step
                header("Location: ../add-passenger-info.php?booking_id=$bookingId");
                exit();
            } catch (Exception $e) {
                $pdo->rollBack();
                $_SESSION['error'] = $e->getMessage();

                // Redirect to the appropriate page based on whether this was a voucher booking
                if (isset($_POST['voucher_booking']) && $_POST['voucher_booking'] == 1) {
                    header("Location: ../booking-voucher.php");
                } else {
                    header("Location: ../booking.php");
                }
                exit();
            }
        }
    }

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['deleteDraft'])) {

            try {
                if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                    throw new Exception("Invalid CSRF token.");
                }

                $booking_id = $_POST['booking_id'];

                $pdo->beginTransaction();

                $stmt = $pdo->prepare("DELETE FROM cb_bookings where booking_id = :booking_id");
                $stmt->execute([
                    ':booking_id' => $booking_id
                ]);

                if ($stmt->rowCount() === 0) {
                    throw new Exception('Failed to delete.');
                }

                $pdo->commit();

                $_SESSION['success'] = "Booking has beed Deleted";
                header("Location: ../booking.php"); // Redirect back to the form
                exit();
            } catch (Exception $e) {
                $pdo->rollBack();
                $_SESSION['error'] = $e->getMessage();
                header("Location: ../booking.php"); // Redirect back to the form
                exit();
            }
        }

        if (isset($_POST['cancelBooking'])) {
            try {
                if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                    throw new Exception("Invalid CSRF token.");
                }

                if (!isset($_SESSION['id'])) {
                    throw new Exception("User not authenticated");
                }

                $booking_id = $_POST['booking_id'];
                $referenceNumber = $_POST['referenceNumber'];

                $pdo->beginTransaction();

                // Get booking details to check if vouchers need to be restored
                $stmt = $pdo->prepare("SELECT bb.*, cp.voucher_use, pl.portName
                                     FROM cb_bookings bb
                                     LEFT JOIN cb_payments cp ON bb.booking_id = cp.booking_id
                                     LEFT JOIN port_list pl ON bb.port_id = pl.id
                                     WHERE bb.booking_id = :booking_id");
                $stmt->execute([':booking_id' => $booking_id]);
                $booking = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$booking) {
                    throw new Exception('Booking not found.');
                }

                // Insert booking data into cb_booking_cancellation table with status 'request'
                $stmt = $pdo->prepare("INSERT INTO cb_booking_cancellation
                    (booking_id, referenceNum, tour_operator_id, resort_operator_id, boat_id, port_id,
                     check_in_date, check_out_date, booking_status, created_by, date_created)
                    VALUES
                    (:booking_id, :referenceNum, :tour_operator_id, :resort_operator_id, :boat_id, :port_id,
                     :check_in_date, :check_out_date, 'request', :created_by, NOW())");

                $stmt->execute([
                    ':booking_id' => $booking['booking_id'],
                    ':referenceNum' => $booking['referenceNum'],
                    ':tour_operator_id' => $booking['tour_operator_id'],
                    ':resort_operator_id' => $booking['resort_operator_id'],
                    ':boat_id' => $booking['boat_id'],
                    ':port_id' => $booking['port_id'],
                    ':check_in_date' => $booking['check_in_date'],
                    ':check_out_date' => $booking['check_out_date'],
                    ':created_by' => $booking['tour_operator_id']
                ]);

                // Restore vouchers if any were used
                // if ($booking['voucher_use'] > 0) {
                //     $voucherTable = (strpos($booking['portName'], 'VINZONS') !== false) ? 'voucher_vinzons' : 'voucher_others';
                //     $voucherField = (strpos($booking['portName'], 'VINZONS') !== false) ? 'voucher_vinzons' : 'voucher_others';

                //     $stmt = $pdo->prepare("UPDATE cb_vouchers SET $voucherField = $voucherField + :voucher_count WHERE id = 1");
                //     $stmt->execute([':voucher_count' => $booking['voucher_use']]);
                // }

                // Insert log entry
                $type = "Booking - Cancelled";
                $description = "Tour operator: " . $_SESSION['username'] . " cancelled booking: " . $referenceNumber . ". Date: " . date("Y-m-d");
                $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
                $stmt->execute([
                    ':type' => $type,
                    ':description' => $description
                ]);

                $pdo->commit();

                $_SESSION['success'] = "Booking cancellation request has been submitted successfully";
                header("Location: ../transaction-pending.php");
                exit();
            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                $_SESSION['error'] = $e->getMessage();
                header("Location: ../transaction-pending.php");
                exit();
            }
        }
    }

    if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['proceedBtn'])) {
        try {
            if (!isset($_SESSION['id'])) {
                throw new Exception("You must be logged in to perform this action.");
            }
            if (
                !isset($_POST['csrf_token'], $_SESSION['csrf_token']) ||
                $_POST['csrf_token'] !== $_SESSION['csrf_token']
            ) {
                throw new Exception("Invalid CSRF token.");
            }
            unset($_SESSION['csrf_token']);

            $bookingId = filter_input(INPUT_POST, 'booking_id', FILTER_VALIDATE_INT);
            $referenceNumber = $_POST['referenceNumber'] ?? '';

            if (!$bookingId) {
                throw new Exception("Invalid booking ID.");
            }

            $details = getBookingDetails($pdo, $bookingId);
            if (!$details || $details['referenceNum'] !== $referenceNumber) {
                throw new Exception("Booking not found or reference mismatch.");
            }

            // counts…
            $counts = getTouristAndCrewCounts($pdo, $bookingId);
            $adults = $counts['adults'] ?? 0;
            $children = $counts['children'] ?? 0;
            $crew = $counts['crewTts'] ?? 0;
            $fee = $details['port_fee'];
            // example: adults only
            $totalAmt = $adults * $fee;

            $pdo->beginTransaction();

            // Check if this is a voucher booking
            if ($details['booking_status'] === 'voucher') {
                // Generate a random voucher number
                $voucherNum = 'VOUCHER-' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 6));

                // Update payment with voucher-specific settings
                $up = $pdo->prepare("
                UPDATE cb_payments SET
                  total_adults   = :adults,
                  total_children = :children,
                  total_crew     = :crew,
                  total_amount   = :amt,
                  payment_status = 'voucher',
                  or_num         = :voucher_num,
                  receipt_image = 'travel-voucher.jpeg'
                WHERE booking_id = :bid
            ");
                $up->execute([
                    ':adults' => $adults,
                    ':children' => $children,
                    ':crew' => $crew,
                    ':amt' => $totalAmt,
                    ':voucher_num' => $voucherNum,
                    ':bid' => $bookingId
                ]);

                // Insert/update approvals with treasurer automatically approved
                $pdo->prepare("
              INSERT INTO cb_booking_approvals
                (booking_id, treasurer)
              VALUES (:bid, 'Approved')
              ON DUPLICATE KEY UPDATE
                treasurer = 'Approved'
            ")->execute([':bid' => $bookingId]);
            } else {
                // Regular non-voucher booking process
                // update or insert payment
                $up = $pdo->prepare("
                UPDATE cb_payments SET
                  total_adults   = :adults,
                  total_children = :children,
                  total_crew     = :crew,
                  total_amount   = :amt
                WHERE booking_id = :bid
            ");
                $up->execute([
                    ':adults' => $adults,
                    ':children' => $children,
                    ':crew' => $crew,
                    ':amt' => $totalAmt,
                    ':bid' => $bookingId
                ]);

                // Regular approvals process
                $pdo->prepare("
              INSERT INTO cb_booking_approvals
                (booking_id)
              VALUES (:bid)
            ")->execute([':bid' => $bookingId]);
            }
            // Update booking status to pending
            $pdo->prepare("
          UPDATE cb_bookings
          SET booking_status = 'pending'
          WHERE booking_id = :bid
        ")->execute([':bid' => $bookingId]);

            $pdo->commit();

            $_SESSION['success'] = $details['booking_status'] === 'voucher' ?
                "Voucher booking processed successfully." :
                "Please wait for approval.";
            header("Location:../transaction-pending.php");
            exit;
        } catch (Exception $e) {
            if ($pdo->inTransaction())
                $pdo->rollBack();
            $_SESSION['error'] = $e->getMessage();
            header("Location:../booking.php");
            exit;
        }
    }

}
