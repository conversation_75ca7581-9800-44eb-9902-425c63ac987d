<?php
session_start();
require_once '../../../../connection/dbconnect.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['id']) || $_SESSION['accountType'] !== 'admin') {
    $_SESSION['error'] = 'Unauthorized access.';
    header('Location: ../../../login.php');
    exit();
}

// CSRF token validation
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    $_SESSION['error'] = 'Invalid CSRF token.';
    header('Location: ../transaction-canceled.php');
    exit();
}

if (isset($_POST['cancellationAction'])) {
    try {
        $booking_id = (int) $_POST['booking_id'];
        $action = $_POST['action']; // 'approve' or 'reject'

        if (!in_array($action, ['approve', 'reject'])) {
            throw new Exception('Invalid action specified.');
        }

        // Start transaction
        $pdo->beginTransaction();

        if ($action === 'approve') {
            // Approve cancellation request

            // Update the cancellation request status to 'canceled'
            $stmt = $pdo->prepare("UPDATE cb_booking_cancellation SET booking_status = 'canceled' WHERE booking_id = :booking_id AND booking_status = 'request'");
            $stmt->execute([':booking_id' => $booking_id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Cancellation request not found or already processed.');
            }

            // Update the main booking status to 'canceled
            $stmt = $pdo->prepare("UPDATE cb_bookings SET booking_status = 'canceled' WHERE booking_id = :booking_id");
            $stmt->execute([':booking_id' => $booking_id]);

            // Update booking approvals to reflect cancellation
            $stmt = $pdo->prepare("
            UPDATE cb_booking_approvals
            SET
                mtho = NULL,
                treasurer = NULL,
                boat = NULL,
                resort = NULL
            WHERE booking_id = :booking_id
            ");
            $stmt->execute([':booking_id' => $booking_id]);

            // Get booking reference for logging
            $stmt = $pdo->prepare("SELECT referenceNum FROM cb_bookings WHERE booking_id = :booking_id");
            $stmt->execute([':booking_id' => $booking_id]);
            $booking = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$booking) {
                throw new Exception('Booking not found.');
            }

            // Insert log entry
            $type = "Booking - Cancellation Approved";
            $description = "Admin: " . $_SESSION['username'] . " approved cancellation request for booking: " . $booking['referenceNum'] . ". Date: " . date("Y-m-d");
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);

            $pdo->commit();
            $_SESSION['success'] = "Cancellation request approved successfully.";

        } else if ($action === 'reject') {
            // Reject cancellation request

            // Handle reject reason
            $rejectReason = '';
            $selectedReason = trim($_POST['reject_reason'] ?? '');
            if ($selectedReason === 'other') {
                $rejectReason = trim($_POST['custom_reject_reason'] ?? '');
            } else {
                $rejectReason = $selectedReason;
            }

            if (empty($rejectReason)) {
                throw new Exception("Reject reason is required when rejecting a cancellation request.");
            }

            // Update the cancellation request status to 'rejected' and add reject reason
            $stmt = $pdo->prepare("UPDATE cb_booking_cancellation SET booking_status = 'rejected', user_reasons = :reject_reason WHERE booking_id = :booking_id AND booking_status = 'request'");
            $stmt->execute([
                ':booking_id' => $booking_id,
                ':reject_reason' => $rejectReason
            ]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Cancellation request not found or already processed.');
            }

            // Get booking reference for logging
            $stmt = $pdo->prepare("SELECT referenceNum FROM cb_bookings WHERE booking_id = :booking_id");
            $stmt->execute([':booking_id' => $booking_id]);
            $booking = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$booking) {
                throw new Exception('Booking not found.');
            }

            // Insert log entry
            $type = "Booking - Cancellation Rejected";
            $description = "Admin: " . $_SESSION['username'] . " rejected cancellation request for booking: " . $booking['referenceNum'] . ". Reason: " . $rejectReason . ". Date: " . date("Y-m-d");
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);

            $pdo->commit();
            $_SESSION['success'] = "Cancellation request rejected successfully.";
        }

        header('Location: ../transaction-canceled.php');
        exit();

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        $_SESSION['error'] = $e->getMessage();
        header('Location: ../transaction-canceled.php');
        exit();
    }
} else {
    $_SESSION['error'] = 'Invalid request.';
    header('Location: ../transaction-canceled.php');
    exit();
}
?>