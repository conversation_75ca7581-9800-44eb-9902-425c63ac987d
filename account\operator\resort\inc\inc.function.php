<?php

function getBookingDetails($pdo, $getResortStatus, $id)
{
    try {
        $sql = "SELECT bb.booking_id AS booking_id,
                bb.booking_status AS booking_status,
                bb.referenceNum,
                bb.check_in_date,
                bb.check_out_date,
                oi.designation AS resort_operator_designation,
                oi2.designation AS tour_operator_designation,
                oi2.firstname,
                oi2.middlename,
                oi2.lastname,
                oi2.extname,
                bob.boatName,
                pl.portName,
                cp.total_adults,
                cp.total_children,
                cp.port_fee,
                cp.payment_status,
                cbc.booking_status AS cancellation_booking_status
        FROM cb_bookings bb
        JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
        JOIN operator_info oi2 ON bb.tour_operator_id = oi2.user_id
        JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
        JOIN port_list pl ON bb.port_id = pl.id
        JOIN cb_payments cp ON bb.booking_id = cp.booking_id
        JOIN cb_booking_approvals cba ON bb.booking_id = cba.booking_id
        LEFT JOIN cb_booking_cancellation cbc 
                ON bb.booking_id = cbc.booking_id 
                AND cbc.booking_status = 'request'
        WHERE cba.resort = :resort
        AND bb.resort_operator_id = :resort_id";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':resort', $getResortStatus, PDO::PARAM_STR);
        $stmt->bindParam(':resort_id', $id, PDO::PARAM_INT);
        $stmt->execute();

        // Fetch all rows
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $rows ?: []; // Return empty array if no rows found
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return [];
    }
}

function getIncompleteBooking(PDO $pdo, $getBookingStatus, $id)
{
    try {
        $sql = "SELECT
                    cbc.booking_id AS booking_id,
                    cbc.referenceNum,
                    cbc.date_created,
                    oi.designation,
                    bob.boatName
                FROM cb_booking_cancellation cbc
                JOIN operator_info oi
                    ON cbc.resort_operator_id = oi.user_id
                JOIN boat_operator_boatlist bob
                    ON cbc.boat_id = bob.id
                WHERE cbc.booking_status = :booking_status
                AND cbc.resort_operator_id = :resort_id
                AND cbc.created_by = :created_by
                ORDER BY cbc.date_created DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':booking_status', $getBookingStatus, PDO::PARAM_STR);
        $stmt->bindParam(':resort_id', $id, PDO::PARAM_INT);
        $stmt->bindParam(':created_by', $id, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching cancellation requests: " . $e->getMessage());
        return false;
    }
}

function getNotification(PDO $pdo, $resort_operator_id): array
{
    $stmt = $pdo->prepare("
        SELECT
            SUM(cba.resort = 'Pending') AS pending_count
        FROM cb_booking_approvals cba
        JOIN cb_bookings bb ON cba.booking_id = bb.booking_id
        WHERE bb.resort_operator_id = :resort_operator_id
    ");
    $stmt->bindParam(':resort_operator_id', $resort_operator_id, PDO::PARAM_STR);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'pending' => (int) ($result['pending_count'] ?? 0)
    ];
}
