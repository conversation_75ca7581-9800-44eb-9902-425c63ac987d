<?php
ob_start();
require '_header.php';
if ($accType === "mtho staff") {
    header("Location: home.php");
    exit;
}



$selectID = 1;

$sql = 'SELECT status_maintenance FROM system WHERE id = :id';
// Prepare and Execute
$stmt = $pdo->prepare($sql);
$stmt->bindParam(':id', $selectID, PDO::PARAM_INT); // Assuming `rememberToken` is a string
$stmt->execute();
// Fetch the Data
$row = $stmt->fetch(PDO::FETCH_ASSOC);

if ($row['status_maintenance'] === 1) {
    $mtState = 0;
    $statusMsg = ' Are you sure you want to <span class="text-green-600 text-bold">DISABLE </span>maintenance mode?';
    $statusText = '<h1 class="text-3xl font-bold uppercase mt-1 text-red-500">Offline</h1>';
    $statusBtn = '
    <button data-modal-target="maintenance-modal" data-modal-toggle="maintenance-modal" type="button" class="bg-red-600 text-white p-3 rounded-lg font-semibold hover:bg-red-700 transition-transform transform hover:scale-105">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M11.412 15.655 9.75 21.75l3.745-4.012M9.257 13.5H3.75l2.659-2.849m2.048-2.194L14.25 2.25 12 10.5h8.25l-4.707 5.043M8.457 8.457 3 3m5.457 5.457 7.086 7.086m0 0L21 21" />
                </svg>
            </button>
    ';
} else {
    $mtState = 1;
    $statusMsg = ' Are you sure you want to <span class="text-red-600 text-bold">ENABLE </span>maintenance mode?';
    $statusText = '<h1 class="text-3xl font-bold uppercase mt-1 text-green-500">Online</h1>';
    $statusBtn = '
      <button data-modal-target="maintenance-modal" data-modal-toggle="maintenance-modal" type="button" class="bg-green-600 text-white p-3 rounded-lg font-semibold hover:bg-green-700 transition-transform transform hover:scale-105">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z" />
                </svg>

            </button>
    ';
}
ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="border p-4 rounded-lg shadow-lg bg-white mt-14">

        <div class="bg-gradient-to-r from-red-600 to-red-700 rounded-xl shadow-lg p-6 mb-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2">
                    <li class="inline-flex items-center">
                        <a href="home.php"
                            class="inline-flex items-center text-sm font-medium text-red-100 hover:text-white transition-colors">
                            <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 text-red-200 mx-1" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="text-sm font-medium text-red-100">System Maintenance</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Title Section -->
            <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4">
                    <i class="fas fa-clock text-white text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-1">System Maintenance</h1>
                    <p class="text-blue-100">Enable or disable system operations</p>
                </div>
            </div>
        </div>

        <div class="flex items-center p-4 my-4 text-sm text-yellow-800 rounded-lg bg-yellow-100" role="alert">
            <svg class="shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="currentColor" viewBox="0 0 20 20">
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
            </svg>
            <span class="sr-only">Info</span>
            <div>
                <span class="font-medium">Reminder!</span> Make sure to make an announcement before switching to
                maintenance mode.
            </div>
        </div>

        <div class="bg-gray-800 text-white p-6 rounded-lg shadow-lg flex justify-between items-center">
            <div>
                <p class="text-sm font-semibold uppercase tracking-wider">System Current Status:</p>
                <?= $statusText; ?>
            </div>
            <?= $statusBtn; ?>
        </div>

        <!-- Change Status Modal -->
        <div id="maintenance-modal" tabindex="-1"
            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-md max-h-full">
                <div class="relative bg-white rounded-lg shadow">
                    <button type="button"
                        class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center"
                        data-modal-hide="maintenance-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>

                    <div class="p-4 md:p-5 text-center">
                        <form action="inc/inc.maintenance.php" method="POST">
                            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12" aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                    stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                            </svg>
                            <input type="hidden" name="csrf_token"
                                value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" name="mtState" value="<?= $mtState; ?>">
                            <input type="hidden" name="adminUsername" value="<?= $row_global['username']; ?>">
                            <h3 class="mb-5 text-lg font-normal text-gray-700">
                                <?= $statusMsg; ?>
                            </h3>
                            <button type="submit" name="maintenanceBtn"
                                class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
                                Yes, I'm sure
                            </button>
                            <button data-modal-hide="maintenance-modal" type="button"
                                class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200">
                                No, cancel
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <?php
    require '_footer.php';
    ?>