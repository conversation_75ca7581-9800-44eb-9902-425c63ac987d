<?php
ob_start();
require '_header.php';
if ($accType === "mtho staff") {
    header("Location: home.php");
    exit;
}
ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="border p-4 rounded-lg shadow-lg bg-white mt-14">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php"
                        class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Voucher List</span>
                    </div>
                </li>
            </ol>
        </nav>

        <h2 class="text-2xl font-bold text-gray-900 mt-6">Voucher List</h2>
        <p class="text-sm text-gray-600 mt-2">Tour Operators Voucher List</p>

        <!-- Modal -->
        <div class="mt-6">
            <!-- Modal toggle -->
            <button data-modal-target="addVoucher-modal" data-modal-toggle="addVoucher-modal"
                class="flex items-center text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 transition-all duration-200"
                type="button">
                <i class="fas fa-ticket-alt mr-2"></i> Add Voucher
            </button>

            <!-- Main modal -->
            <div id="addVoucher-modal" tabindex="-1" aria-hidden="true"
                class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-md max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow-lg">
                        <!-- Modal header -->
                        <div
                            class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-blue-700 rounded-t-lg">
                            <div class="flex items-center">
                                <i class="fas fa-ticket-alt text-white mr-2"></i>
                                <h3 class="text-xl font-semibold text-white">
                                    Add Voucher
                                </h3>
                            </div>
                            <button type="button"
                                class="text-white bg-blue-800 hover:bg-blue-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center transition-colors duration-200"
                                data-modal-hide="addVoucher-modal">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>

                        <div class="p-5 space-y-5">
                            <form action="inc/inc.voucher.php" method="POST">
                                <input type="hidden" name="csrf_token"
                                    value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">

                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-user-tie text-blue-600 mr-2"></i>
                                        <span class="text-sm font-medium text-gray-700">Tour Operator</span>
                                    </div>
                                    <select name="operatorId"
                                        class="w-full p-2.5 bg-white border border-gray-300 text-gray-700 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                        required>
                                        <option value="">Select Tour Operator</option>
                                        <?php
                                        try {
                                            $sqlOperators = "SELECT
                                                oi.user_id,
                                                CONCAT(oi.firstname, ' ', oi.lastname) AS operator_name
                                            FROM
                                                operator_info oi
                                            INNER JOIN
                                                operator_account oa
                                            ON
                                                oi.user_id = oa.id
                                            WHERE
                                                oi.operatorType = 'Tour operator'
                                            AND
                                                oa.accountStatus = 'Activated'
                                            ORDER BY
                                                oi.firstname ASC";

                                            $stmtOperators = $pdo->query($sqlOperators);
                                            $operators = $stmtOperators->fetchAll(PDO::FETCH_ASSOC);

                                            foreach ($operators as $operator) {
                                                echo '<option value="' . htmlspecialchars($operator['user_id']) . '">' .
                                                    htmlspecialchars($operator['operator_name']) .
                                                    '</option>';
                                            }
                                        } catch (PDOException $e) {
                                            echo '<option value="">Error loading operators</option>';
                                        }
                                        ?>
                                    </select>
                                </div>

                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-tags text-blue-600 mr-2"></i>
                                        <span class="text-sm font-medium text-gray-700">Voucher Type</span>
                                    </div>
                                    <select name="voucherType"
                                        class="w-full p-2.5 bg-white border border-gray-300 text-gray-700 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                        required>
                                        <option value="">Select Voucher Type</option>
                                        <option value="vinzons">Vinzons Voucher</option>
                                        <option value="others">Other Vouchers</option>
                                    </select>
                                </div>

                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-calculator text-blue-600 mr-2"></i>
                                        <span class="text-sm font-medium text-gray-700">Voucher Count</span>
                                    </div>
                                    <input type="number" name="voucherCount"
                                        class="w-full p-2.5 bg-white border border-gray-300 text-gray-700 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                        min="1" placeholder="0" required>
                                </div>

                                <div
                                    class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-200 rounded-b mt-4">
                                    <button data-modal-hide="addVoucher-modal" type="button"
                                        class="flex items-center py-2.5 px-5 me-3 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                        <i class="fas fa-times-circle mr-1.5"></i> Cancel
                                    </button>
                                    <button type="submit" name="addVoucherBtn"
                                        class="flex items-center py-2.5 px-5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 transition-all duration-200">
                                        <i class="fas fa-plus-circle mr-1.5"></i> Add Voucher
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="overflow-x-auto mt-4">
            <table id="search-table" class="min-w-full bg-white border border-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour
                            Operator</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Vinzons Vouchers</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Other Vouchers</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Action</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        // Fetch data from the database with operator information
                        $sql = "SELECT
                                    cv.operator_id,
                                    cv.voucher_vinzons,
                                    cv.voucher_others,
                                    CONCAT(oi.firstname, ' ', oi.lastname) AS operator_name
                                FROM
                                    cb_vouchers cv
                                JOIN
                                    operator_info oi ON cv.operator_id = oi.user_id
                                ORDER BY
                                    oi.firstname ASC";
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute();

                        // Fetch the data
                        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        if ($rows) {
                            foreach ($rows as $row) {
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                        <?= htmlspecialchars($row['operator_name']); ?></td>
                                    <td class="px-6 py-4 text-sm text-gray-500 text-center">
                                        <?= htmlspecialchars($row['voucher_vinzons']); ?></td>
                                    <td class="px-6 py-4 text-sm text-gray-500 text-center">
                                        <?= htmlspecialchars($row['voucher_others']); ?></td>
                                    <td class="px-6 py-4 text-sm text-center">
                                        <button data-modal-target="delete-voucher-modal-<?= $row['operator_id']; ?>"
                                            data-modal-toggle="delete-voucher-modal-<?= $row['operator_id']; ?>"
                                            class="inline-flex items-center justify-center bg-red-600 hover:bg-red-700 text-white font-medium rounded-md text-xs px-3 py-1.5 transition-all duration-200 focus:ring-2 focus:ring-red-400 focus:outline-none"
                                            type="button">
                                            <i class="fas fa-trash-alt mr-1"></i> Delete
                                        </button>
                                    </td>
                                </tr>

                                <!-- Delete Modal -->
                                <div id="delete-voucher-modal-<?= $row['operator_id']; ?>" tabindex="-1"
                                    class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                            <!-- Modal Header -->
                                            <div
                                                class="flex items-center justify-between p-4 bg-gradient-to-r from-red-600 to-red-700 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-trash-alt text-white mr-2"></i>
                                                    <h3 class="text-xl font-semibold text-white">
                                                        Delete Vouchers
                                                    </h3>
                                                </div>
                                                <button type="button"
                                                    class="text-white bg-red-800 hover:bg-red-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center transition-colors duration-200"
                                                    data-modal-hide="delete-voucher-modal-<?= $row['operator_id']; ?>">
                                                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                        fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <div class="p-5 md:p-6">
                                                <form action="inc/inc.voucher.php" method="POST" class="text-center">
                                                    <div class="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                                                        <div class="flex justify-center mb-4">
                                                            <div
                                                                class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 text-red-600">
                                                                <i class="fas fa-exclamation-triangle text-2xl"></i>
                                                            </div>
                                                        </div>
                                                        <input type="hidden" name="csrf_token"
                                                            value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                        <input type="hidden" value="<?= $row['operator_id']; ?>" name="rowId">
                                                        <h3 class="text-lg font-medium text-gray-800 mb-2">
                                                            Confirm Deletion
                                                        </h3>
                                                        <p class="text-gray-600 mb-4">
                                                            Are you sure you want to <span class="text-red-600 font-bold">delete all
                                                                vouchers</span> for this operator?
                                                        </p>
                                                        <p class="text-sm text-gray-500 mb-4">
                                                            This action cannot be undone.
                                                        </p>
                                                    </div>

                                                    <div class="flex justify-center space-x-4">
                                                        <button data-modal-hide="delete-voucher-modal-<?= $row['operator_id']; ?>"
                                                            type="button"
                                                            class="flex items-center py-2.5 px-5 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                            <i class="fas fa-times-circle mr-1.5"></i> Cancel
                                                        </button>
                                                        <button type="submit" name="deleteVoucher"
                                                            class="flex items-center py-2.5 px-5 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200">
                                                            <i class="fas fa-trash-alt mr-1.5"></i> Delete
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php
                            }
                        } else {
                            ?>
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">
                                    No vouchers found. Add vouchers using the button above.
                                </td>
                            </tr>
                            <?php
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="4" class="px-6 py-4 text-center text-sm text-red-600">
                                Error: <?= htmlspecialchars($e->getMessage()); ?>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>



    <?php
    require '_footer.php';
    ?>